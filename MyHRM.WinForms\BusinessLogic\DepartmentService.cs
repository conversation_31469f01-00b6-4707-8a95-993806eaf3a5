using MyHRM.WinForms.DataAccess;
using MyHRM.WinForms.Models;

namespace MyHRM.WinForms.BusinessLogic
{
    /// <summary>
    /// Business logic layer for department operations
    /// </summary>
    public class DepartmentService
    {
        private readonly OracleDataAccess _dataAccess;

        public DepartmentService()
        {
            _dataAccess = new OracleDataAccess();
        }

        /// <summary>
        /// Add a new department with validation
        /// </summary>
        public async Task<int> AddDepartmentAsync(string name, string description, int? managerId, decimal? budget)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Department name cannot be empty.");

            if (name.Length > 100)
                throw new ArgumentException("Department name cannot exceed 100 characters.");

            if (!string.IsNullOrEmpty(description) && description.Length > 500)
                throw new ArgumentException("Department description cannot exceed 500 characters.");

            if (budget.HasValue && budget.Value < 0)
                throw new ArgumentException("Budget cannot be negative.");

            if (budget.HasValue && budget.Value > 99999999.99m)
                throw new ArgumentException("Budget cannot exceed $99,999,999.99.");

            return await _dataAccess.AddDepartmentAsync(name.Trim(), description?.Trim(), managerId, budget);
        }

        /// <summary>
        /// Get all departments
        /// </summary>
        public async Task<List<Department>> GetAllDepartmentsAsync()
        {
            return await _dataAccess.GetAllDepartmentsAsync();
        }

        /// <summary>
        /// Get department name by ID
        /// </summary>
        public async Task<string> GetDepartmentNameAsync(int departmentId)
        {
            if (departmentId <= 0)
                throw new ArgumentException("Invalid department ID.");

            return await _dataAccess.GetDepartmentNameAsync(departmentId);
        }

        /// <summary>
        /// Update department with validation
        /// </summary>
        public async Task UpdateDepartmentAsync(int departmentId, string name, string description, int? managerId, decimal? budget)
        {
            // Validation
            if (departmentId <= 0)
                throw new ArgumentException("Invalid department ID.");

            if (!string.IsNullOrEmpty(name))
            {
                if (name.Trim().Length == 0)
                    throw new ArgumentException("Department name cannot be empty.");

                if (name.Length > 100)
                    throw new ArgumentException("Department name cannot exceed 100 characters.");
            }

            if (!string.IsNullOrEmpty(description) && description.Length > 500)
                throw new ArgumentException("Department description cannot exceed 500 characters.");

            if (budget.HasValue && budget.Value < 0)
                throw new ArgumentException("Budget cannot be negative.");

            if (budget.HasValue && budget.Value > 99999999.99m)
                throw new ArgumentException("Budget cannot exceed $99,999,999.99.");

            await _dataAccess.UpdateDepartmentAsync(departmentId, name?.Trim(), description?.Trim(), managerId, budget);
        }

        /// <summary>
        /// Assign manager to department with validation
        /// </summary>
        public async Task AssignManagerAsync(int departmentId, int managerId)
        {
            if (departmentId <= 0)
                throw new ArgumentException("Invalid department ID.");

            if (managerId <= 0)
                throw new ArgumentException("Invalid manager ID.");

            await _dataAccess.AssignManagerAsync(departmentId, managerId);
        }

        /// <summary>
        /// Get department audit logs
        /// </summary>
        public async Task<List<DepartmentLog>> GetDepartmentLogsAsync()
        {
            return await _dataAccess.GetDepartmentLogsAsync();
        }

        /// <summary>
        /// Get employees for manager selection
        /// </summary>
        public async Task<List<Employee>> GetEmployeesForSelectionAsync()
        {
            return await _dataAccess.GetEmployeesForSelectionAsync();
        }

        /// <summary>
        /// Validate department data
        /// </summary>
        public (bool IsValid, string ErrorMessage) ValidateDepartment(string name, string description, int? managerId, decimal? budget)
        {
            if (string.IsNullOrWhiteSpace(name))
                return (false, "Department name is required.");

            if (name.Length > 100)
                return (false, "Department name cannot exceed 100 characters.");

            if (!string.IsNullOrEmpty(description) && description.Length > 500)
                return (false, "Department description cannot exceed 500 characters.");

            if (budget.HasValue && budget.Value < 0)
                return (false, "Budget cannot be negative.");

            if (budget.HasValue && budget.Value > 99999999.99m)
                return (false, "Budget cannot exceed $99,999,999.99.");

            return (true, string.Empty);
        }

        /// <summary>
        /// Format budget for display
        /// </summary>
        public string FormatBudget(decimal? budget)
        {
            return budget.HasValue ? budget.Value.ToString("C2") : "Not Set";
        }

        /// <summary>
        /// Get department statistics
        /// </summary>
        public async Task<(int TotalDepartments, decimal TotalBudget, int DepartmentsWithManagers)> GetDepartmentStatisticsAsync()
        {
            var departments = await GetAllDepartmentsAsync();
            
            var totalDepartments = departments.Count;
            var totalBudget = departments.Where(d => d.Budget.HasValue).Sum(d => d.Budget.Value);
            var departmentsWithManagers = departments.Count(d => d.ManagerId.HasValue);

            return (totalDepartments, totalBudget, departmentsWithManagers);
        }
    }
}
