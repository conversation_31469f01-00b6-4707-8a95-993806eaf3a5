using MyHRM.WinForms.DataAccess;
using MyHRM.WinForms.Models;

namespace MyHRM.WinForms.BusinessLogic
{
    /// <summary>
    /// Business logic layer for employee operations
    /// </summary>
    public class EmployeeService
    {
        private readonly OracleDataAccess _dataAccess;

        public EmployeeService()
        {
            _dataAccess = new OracleDataAccess();
        }

        /// <summary>
        /// Test database connectivity
        /// </summary>
        public async Task<bool> TestDatabaseConnectionAsync()
        {
            return await _dataAccess.TestConnectionAsync();
        }

        /// <summary>
        /// Add a new employee with validation
        /// </summary>
        public async Task<int> AddEmployeeAsync(string name, DateTime hireDate, decimal salary)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Employee name cannot be empty.");

            if (name.Length > 100)
                throw new ArgumentException("Employee name cannot exceed 100 characters.");

            if (hireDate > DateTime.Now)
                throw new ArgumentException("Hire date cannot be in the future.");

            if (salary < 0)
                throw new ArgumentException("Salary cannot be negative.");

            if (salary > 999999.99m)
                throw new ArgumentException("Salary cannot exceed $999,999.99.");

            return await _dataAccess.AddEmployeeAsync(name.Trim(), hireDate, salary);
        }

        /// <summary>
        /// Get all employees
        /// </summary>
        public async Task<List<Employee>> GetAllEmployeesAsync()
        {
            return await _dataAccess.GetAllEmployeesAsync();
        }

        /// <summary>
        /// Get employee salary by ID
        /// </summary>
        public async Task<decimal> GetEmployeeSalaryAsync(int employeeId)
        {
            if (employeeId <= 0)
                throw new ArgumentException("Employee ID must be greater than zero.");

            return await _dataAccess.GetEmployeeSalaryAsync(employeeId);
        }

        /// <summary>
        /// Get employee audit logs
        /// </summary>
        public async Task<List<EmployeeLog>> GetEmployeeLogsAsync()
        {
            return await _dataAccess.GetEmployeeLogsAsync();
        }

        /// <summary>
        /// Validate employee data
        /// </summary>
        public (bool IsValid, string ErrorMessage) ValidateEmployee(string name, DateTime hireDate, decimal salary)
        {
            if (string.IsNullOrWhiteSpace(name))
                return (false, "Employee name is required.");

            if (name.Length > 100)
                return (false, "Employee name cannot exceed 100 characters.");

            if (hireDate > DateTime.Now)
                return (false, "Hire date cannot be in the future.");

            if (salary < 0)
                return (false, "Salary cannot be negative.");

            if (salary > 999999.99m)
                return (false, "Salary cannot exceed $999,999.99.");

            return (true, string.Empty);
        }

        /// <summary>
        /// Format salary for display
        /// </summary>
        public string FormatSalary(decimal salary)
        {
            return $"${salary:N2}";
        }

        /// <summary>
        /// Get employee count
        /// </summary>
        public async Task<int> GetEmployeeCountAsync()
        {
            var employees = await GetAllEmployeesAsync();
            return employees.Count;
        }
    }
}
