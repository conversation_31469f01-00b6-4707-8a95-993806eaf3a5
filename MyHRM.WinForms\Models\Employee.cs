using System;

namespace MyHRM.WinForms.Models
{
    /// <summary>
    /// Employee data model representing the employee entity
    /// </summary>
    public class Employee
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime HireDate { get; set; }
        public decimal Salary { get; set; }

        public Employee()
        {
            HireDate = DateTime.Now;
        }

        public Employee(int id, string name, DateTime hireDate, decimal salary)
        {
            Id = id;
            Name = name;
            HireDate = hireDate;
            Salary = salary;
        }

        public override string ToString()
        {
            return $"ID: {Id}, Name: {Name}, Hire Date: {HireDate:yyyy-MM-dd}, Salary: ${Salary:N2}";
        }
    }

    /// <summary>
    /// Employee log entry for audit trail
    /// </summary>
    public class EmployeeLog
    {
        public int LogId { get; set; }
        public int EmployeeId { get; set; }
        public string Action { get; set; } = string.Empty;
        public DateTime LogDate { get; set; }

        public override string ToString()
        {
            return $"Log ID: {LogId}, Employee ID: {EmployeeId}, Action: {Action}, Date: {LogDate:yyyy-MM-dd HH:mm:ss}";
        }
    }
}
