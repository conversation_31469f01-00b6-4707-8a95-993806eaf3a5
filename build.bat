@echo off
echo ========================================
echo MyHRM - HR Management System
echo Build Script
echo ========================================
echo.

echo Restoring NuGet packages...
dotnet restore MyHRM.WinForms/MyHRM.WinForms.csproj
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)

echo.
echo Building project...
dotnet build MyHRM.WinForms/MyHRM.WinForms.csproj --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo To run the application:
echo   cd MyHRM.WinForms
echo   dotnet run
echo.
echo Make sure Oracle database is running and
echo connection string is configured correctly.
echo ========================================
pause
