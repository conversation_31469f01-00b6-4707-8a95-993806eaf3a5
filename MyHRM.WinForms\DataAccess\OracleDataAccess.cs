using Oracle.ManagedDataAccess.Client;
using System.Configuration;
using System.Data;
using MyHRM.WinForms.Models;

namespace MyHRM.WinForms.DataAccess
{
    /// <summary>
    /// Oracle database access layer for HR Management System
    /// </summary>
    public class OracleDataAccess
    {
        private readonly string _connectionString;

        public OracleDataAccess()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["OracleConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("Oracle connection string not found in configuration.");
        }

        /// <summary>
        /// Test database connection
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();
                return connection.State == ConnectionState.Open;
            }
            catch (Exception ex)
            {
                throw new Exception($"Database connection failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Add employee using stored procedure
        /// </summary>
        public async Task<int> AddEmployeeAsync(string name, DateTime hireDate, decimal salary)
        {
            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("BEGIN emp_pkg.add_employee(:p_name, :p_hire_date, :p_salary, :p_employee_id); END;", connection)
                {
                    CommandType = CommandType.Text
                };

                // Input parameters
                command.Parameters.Add("p_name", OracleDbType.Varchar2, name, ParameterDirection.Input);
                command.Parameters.Add("p_hire_date", OracleDbType.Date, hireDate, ParameterDirection.Input);
                command.Parameters.Add("p_salary", OracleDbType.Decimal, salary, ParameterDirection.Input);

                // Output parameter for employee ID
                var outputParam = new OracleParameter("p_employee_id", OracleDbType.Int32)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                await command.ExecuteNonQueryAsync();

                // Return the generated employee ID
                return outputParam.Value != null && outputParam.Value != DBNull.Value
                    ? Convert.ToInt32(outputParam.Value)
                    : 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error adding employee: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get all employees
        /// </summary>
        public async Task<List<Employee>> GetAllEmployeesAsync()
        {
            var employees = new List<Employee>();

            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("SELECT id, name, hire_date, salary FROM employees ORDER BY id", connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    employees.Add(new Employee
                    {
                        Id = reader.GetInt32("id"),
                        Name = reader.GetString("name"),
                        HireDate = reader.GetDateTime("hire_date"),
                        Salary = reader.GetDecimal("salary")
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving employees: {ex.Message}", ex);
            }

            return employees;
        }

        /// <summary>
        /// Get employee salary by ID using function
        /// </summary>
        public async Task<decimal> GetEmployeeSalaryAsync(int employeeId)
        {
            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("SELECT emp_pkg.get_salary_by_id(:p_employee_id) FROM dual", connection);
                command.Parameters.Add("p_employee_id", OracleDbType.Int32, employeeId, ParameterDirection.Input);

                var result = await command.ExecuteScalarAsync();
                return result != null && result != DBNull.Value ? Convert.ToDecimal(result) : 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting employee salary: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get employee audit logs
        /// </summary>
        public async Task<List<EmployeeLog>> GetEmployeeLogsAsync()
        {
            var logs = new List<EmployeeLog>();

            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("SELECT log_id, employee_id, action, log_date FROM employee_log ORDER BY log_date DESC", connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    logs.Add(new EmployeeLog
                    {
                        LogId = reader.GetInt32("log_id"),
                        EmployeeId = reader.GetInt32("employee_id"),
                        Action = reader.GetString("action"),
                        LogDate = reader.GetDateTime("log_date")
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving employee logs: {ex.Message}", ex);
            }

            return logs;
        }

        // =====================================================
        // DEPARTMENT METHODS
        // =====================================================

        /// <summary>
        /// Add department using stored procedure
        /// </summary>
        public async Task<int> AddDepartmentAsync(string name, string description, int? managerId, decimal? budget)
        {
            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("BEGIN dept_pkg.add_department(:p_name, :p_description, :p_manager_id, :p_budget, :p_department_id); END;", connection)
                {
                    CommandType = CommandType.Text
                };

                // Input parameters
                command.Parameters.Add("p_name", OracleDbType.Varchar2, name, ParameterDirection.Input);
                command.Parameters.Add("p_description", OracleDbType.Varchar2, description ?? (object)DBNull.Value, ParameterDirection.Input);
                command.Parameters.Add("p_manager_id", OracleDbType.Int32, managerId ?? (object)DBNull.Value, ParameterDirection.Input);
                command.Parameters.Add("p_budget", OracleDbType.Decimal, budget ?? (object)DBNull.Value, ParameterDirection.Input);

                // Output parameter for department ID
                var outputParam = new OracleParameter("p_department_id", OracleDbType.Int32)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                await command.ExecuteNonQueryAsync();

                // Return the generated department ID
                return outputParam.Value != null && outputParam.Value != DBNull.Value
                    ? Convert.ToInt32(outputParam.Value)
                    : 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error adding department: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get all departments with manager names and employee counts
        /// </summary>
        public async Task<List<Department>> GetAllDepartmentsAsync()
        {
            var departments = new List<Department>();

            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand(@"
                    SELECT d.id, d.name, d.description, d.manager_id, d.budget, d.created_date,
                           e.name as manager_name,
                           (SELECT COUNT(*) FROM employees WHERE department_id = d.id) as employee_count
                    FROM departments d
                    LEFT JOIN employees e ON d.manager_id = e.id
                    ORDER BY d.id", connection);

                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    departments.Add(new Department
                    {
                        Id = reader.GetInt32("id"),
                        Name = reader.GetString("name"),
                        Description = reader.IsDBNull("description") ? string.Empty : reader.GetString("description"),
                        ManagerId = reader.IsDBNull("manager_id") ? null : reader.GetInt32("manager_id"),
                        ManagerName = reader.IsDBNull("manager_name") ? string.Empty : reader.GetString("manager_name"),
                        Budget = reader.IsDBNull("budget") ? null : reader.GetDecimal("budget"),
                        CreatedDate = reader.GetDateTime("created_date"),
                        EmployeeCount = reader.GetInt32("employee_count")
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving departments: {ex.Message}", ex);
            }

            return departments;
        }

        /// <summary>
        /// Get department name by ID using function
        /// </summary>
        public async Task<string> GetDepartmentNameAsync(int departmentId)
        {
            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("SELECT dept_pkg.get_department_by_id(:p_department_id) FROM dual", connection);
                command.Parameters.Add("p_department_id", OracleDbType.Int32, departmentId, ParameterDirection.Input);

                var result = await command.ExecuteScalarAsync();
                return result != null && result != DBNull.Value ? result.ToString() ?? string.Empty : string.Empty;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting department name: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Update department using stored procedure
        /// </summary>
        public async Task UpdateDepartmentAsync(int departmentId, string name, string description, int? managerId, decimal? budget)
        {
            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("BEGIN dept_pkg.update_department(:p_department_id, :p_name, :p_description, :p_manager_id, :p_budget); END;", connection)
                {
                    CommandType = CommandType.Text
                };

                // Parameters
                command.Parameters.Add("p_department_id", OracleDbType.Int32, departmentId, ParameterDirection.Input);
                command.Parameters.Add("p_name", OracleDbType.Varchar2, name ?? (object)DBNull.Value, ParameterDirection.Input);
                command.Parameters.Add("p_description", OracleDbType.Varchar2, description ?? (object)DBNull.Value, ParameterDirection.Input);
                command.Parameters.Add("p_manager_id", OracleDbType.Int32, managerId ?? (object)DBNull.Value, ParameterDirection.Input);
                command.Parameters.Add("p_budget", OracleDbType.Decimal, budget ?? (object)DBNull.Value, ParameterDirection.Input);

                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error updating department: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Assign manager to department using stored procedure
        /// </summary>
        public async Task AssignManagerAsync(int departmentId, int managerId)
        {
            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("BEGIN dept_pkg.assign_manager(:p_department_id, :p_manager_id); END;", connection)
                {
                    CommandType = CommandType.Text
                };

                command.Parameters.Add("p_department_id", OracleDbType.Int32, departmentId, ParameterDirection.Input);
                command.Parameters.Add("p_manager_id", OracleDbType.Int32, managerId, ParameterDirection.Input);

                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error assigning manager: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get department audit logs
        /// </summary>
        public async Task<List<DepartmentLog>> GetDepartmentLogsAsync()
        {
            var logs = new List<DepartmentLog>();

            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("SELECT log_id, department_id, action, log_date, old_budget, new_budget, old_manager_id, new_manager_id FROM department_log ORDER BY log_date DESC", connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    logs.Add(new DepartmentLog
                    {
                        LogId = reader.GetInt32("log_id"),
                        DepartmentId = reader.GetInt32("department_id"),
                        Action = reader.GetString("action"),
                        LogDate = reader.GetDateTime("log_date"),
                        OldBudget = reader.IsDBNull("old_budget") ? null : reader.GetDecimal("old_budget"),
                        NewBudget = reader.IsDBNull("new_budget") ? null : reader.GetDecimal("new_budget"),
                        OldManagerId = reader.IsDBNull("old_manager_id") ? null : reader.GetInt32("old_manager_id"),
                        NewManagerId = reader.IsDBNull("new_manager_id") ? null : reader.GetInt32("new_manager_id")
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving department logs: {ex.Message}", ex);
            }

            return logs;
        }

        /// <summary>
        /// Get employees for dropdown/selection (for manager assignment)
        /// </summary>
        public async Task<List<Employee>> GetEmployeesForSelectionAsync()
        {
            var employees = new List<Employee>();

            try
            {
                using var connection = new OracleConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new OracleCommand("SELECT id, name FROM employees ORDER BY name", connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    employees.Add(new Employee
                    {
                        Id = reader.GetInt32("id"),
                        Name = reader.GetString("name")
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving employees for selection: {ex.Message}", ex);
            }

            return employees;
        }
    }
}

