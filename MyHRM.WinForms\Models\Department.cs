using System;

namespace MyHRM.WinForms.Models
{
    /// <summary>
    /// Department data model representing the department entity
    /// </summary>
    public class Department
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int? ManagerId { get; set; }
        public string ManagerName { get; set; } = string.Empty;
        public decimal? Budget { get; set; }
        public DateTime CreatedDate { get; set; }
        public int EmployeeCount { get; set; }

        public Department()
        {
            CreatedDate = DateTime.Now;
        }

        public Department(int id, string name, string description, int? managerId, decimal? budget)
        {
            Id = id;
            Name = name;
            Description = description;
            ManagerId = managerId;
            Budget = budget;
            CreatedDate = DateTime.Now;
        }

        public override string ToString()
        {
            return $"ID: {Id}, Name: {Name}, Manager: {ManagerName ?? "None"}, Budget: {Budget:C2}, Employees: {EmployeeCount}";
        }
    }

    /// <summary>
    /// Department log entry for audit trail
    /// </summary>
    public class DepartmentLog
    {
        public int LogId { get; set; }
        public int DepartmentId { get; set; }
        public string Action { get; set; } = string.Empty;
        public DateTime LogDate { get; set; }
        public decimal? OldBudget { get; set; }
        public decimal? NewBudget { get; set; }
        public int? OldManagerId { get; set; }
        public int? NewManagerId { get; set; }

        public override string ToString()
        {
            return $"Log ID: {LogId}, Department ID: {DepartmentId}, Action: {Action}, Date: {LogDate:yyyy-MM-dd HH:mm:ss}";
        }
    }
}
