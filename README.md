# MyHRM - HR Management System

A comprehensive HR Management System built with C# .NET 6 WinForms and Oracle 11g database, featuring advanced PL/SQL programming constructs.

## 🚀 Features

### Windows Forms Application
- **Modern GUI**: Clean, intuitive interface for employee management
- **Employee Management**: Add, view, and manage employee records
- **Real-time Validation**: Input validation with user-friendly error messages
- **Database Integration**: Seamless Oracle database connectivity
- **PL/SQL Testing**: Built-in functionality to test all PL/SQL features

### Oracle Database with Advanced PL/SQL
- **Complete Schema**: Tables, sequences, indexes for optimal performance
- **PL/SQL Package**: Comprehensive package with procedures and functions
- **Triggers**: Automatic audit logging and data validation
- **All PL/SQL Constructs**:
  - ✅ Variables and Constants
  - ✅ Explicit Cursors
  - ✅ Conditional Logic (IF statements)
  - ✅ Loops (FOR, WHILE, Basic)
  - ✅ Stored Procedures
  - ✅ Functions
  - ✅ Triggers
  - ✅ Packages
  - ✅ Exception Handling

## 📋 Prerequisites

### Software Requirements
- **Visual Studio 2022** (or later) with .NET 6 support
- **Oracle Database 11g** (or later)
- **Oracle Client** (for connectivity)

### NuGet Packages (Auto-installed)
- `Oracle.ManagedDataAccess.Core` (v3.21.120)
- `System.Configuration.ConfigurationManager` (v7.0.0)

## 🛠️ Installation & Setup

### 1. Database Setup

#### Step 1: Create Oracle User (Optional)
```sql
-- Connect as SYSTEM or DBA
CREATE USER ** IDENTIFIED BY **;
GRANT CONNECT, RESOURCE, CREATE VIEW, CREATE SEQUENCE, CREATE TRIGGER TO **;
GRANT UNLIMITED TABLESPACE TO **;
```

#### Step 2: Run Database Scripts
```bash
# Connect to Oracle as ** user (or your preferred user)
sqlplus **/**@localhost:1521/XE

# Run the schema creation script
@Database/CreateSchema.sql

# Load sample data
@Database/SampleData.sql
```

### 2. Application Setup

#### Step 1: Update Connection String
Edit `MyHRM.WinForms/App.config`:
```xml
<connectionStrings>
    <add name="OracleConnection" 
         connectionString="Data Source=localhost:1521/XE;User Id=**;Password=**;Connection Timeout=30;" />
</connectionStrings>
```

#### Step 2: Build and Run
```bash
# Navigate to project directory
cd MyHRM.WinForms

# Restore packages and build
dotnet restore
dotnet build

# Run the application
dotnet run
```

## 🎯 Usage Guide

### Main Application Features

#### 1. Add Employee
- Enter employee name (required, max 100 characters)
- Select hire date (cannot be future date)
- Enter salary (positive number, max $999,999.99)
- Click "Add Employee" to save

#### 2. View Employees
- Click "View Employees" to display all employees in the grid
- Data includes ID, Name, Hire Date, and Salary
- Automatically formatted for easy reading

#### 3. Test Database Connection
- Click "Test Connection" to verify Oracle connectivity
- Displays success/failure message with details

#### 4. Test PL/SQL Features
- Click "Test PL/SQL" to demonstrate all PL/SQL constructs
- Adds sample employee using stored procedure
- Retrieves salary using function
- Shows audit logs created by triggers
- Comprehensive test of all database features

#### 5. View Audit Logs
- Click "View Logs" to see employee audit trail
- Shows all database operations (INSERT, UPDATE, DELETE)
- Includes timestamps and salary change tracking

## 🗄️ Database Schema

### Tables
- **employees**: Main employee data (id, name, hire_date, salary, created_date)
- **employee_log**: Audit trail (log_id, employee_id, action, log_date, old_salary, new_salary)

### Sequences
- **emp_seq**: Auto-increment for employee IDs
- **log_seq**: Auto-increment for log IDs

### PL/SQL Package: emp_pkg
```sql
-- Procedures
emp_pkg.add_employee(name, hire_date, salary, OUT employee_id)
emp_pkg.update_employee_salary(employee_id, new_salary)

-- Functions
emp_pkg.get_salary_by_id(employee_id) RETURN NUMBER
emp_pkg.get_employee_count RETURN NUMBER
emp_pkg.calculate_annual_bonus(employee_id, bonus_percentage) RETURN NUMBER
```

### Triggers
- **trg_employee_audit**: Logs all INSERT/UPDATE/DELETE operations
- **trg_employee_validation**: Validates data before INSERT/UPDATE

## 🧪 Testing

### Manual Testing
1. **Connection Test**: Use "Test Connection" button
2. **Add Employee**: Try various valid/invalid inputs
3. **PL/SQL Test**: Use "Test PL/SQL" button for comprehensive testing
4. **View Data**: Check employees and logs display correctly

### Sample Test Cases
```csharp
// Valid employee data
Name: "John Doe"
Hire Date: 2023-01-15
Salary: 75000.00

// Invalid test cases
Name: "" (empty - should fail)
Hire Date: 2025-01-01 (future - should fail)
Salary: -1000 (negative - should fail)
```

## 🏗️ Architecture

### Application Layers
- **Presentation Layer**: WinForms UI (MainForm)
- **Business Logic Layer**: EmployeeService (validation, business rules)
- **Data Access Layer**: OracleDataAccess (database operations)
- **Data Models**: Employee and EmployeeLog classes

### Design Patterns
- **Repository Pattern**: Data access abstraction
- **Service Layer**: Business logic separation
- **Model-View Pattern**: UI and data separation

## 🔧 Configuration

### Connection String Parameters
```xml
Data Source=hostname:port/service_name
User Id=username
Password=password
Connection Timeout=30
```

### Application Settings
- Application name and version in App.config
- Configurable connection timeout
- Error handling and logging

## 📝 Sample Data

The system includes 10 sample employees with realistic data:
- Various hire dates (2017-2023)
- Salary range: $55,000 - $95,000
- Diverse names for testing
- Automatic audit log generation

## 🚨 Troubleshooting

### Common Issues

#### Oracle Connection Errors
```
Error: ORA-12154: TNS:could not resolve the connect identifier
Solution: Check Oracle client installation and connection string
```

#### Package Compilation Errors
```
Error: PLS-00201: identifier 'EMP_PKG' must be declared
Solution: Run CreateSchema.sql script completely
```

#### Permission Errors
```
Error: ORA-00942: table or view does not exist
Solution: Grant proper permissions to database user
```

### Debug Steps
1. Verify Oracle service is running
2. Test connection with SQL*Plus
3. Check user permissions
4. Validate connection string format
5. Review application logs

## 📚 Learning Objectives

This project demonstrates:
- **C# .NET 6**: Modern Windows Forms development
- **Oracle Integration**: Professional database connectivity
- **PL/SQL Mastery**: All major PL/SQL constructs
- **Software Architecture**: Layered application design
- **Error Handling**: Comprehensive exception management
- **Data Validation**: Client and server-side validation
- **Audit Logging**: Database trigger implementation

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👨‍💻 Author

Created as a comprehensive demonstration of C# .NET 6 WinForms with Oracle 11g database integration and advanced PL/SQL programming.

---

**Ready to run!** Follow the setup instructions and start exploring the comprehensive HR Management System with advanced PL/SQL features.
