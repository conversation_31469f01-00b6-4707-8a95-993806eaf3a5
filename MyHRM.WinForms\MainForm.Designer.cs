namespace MyHRM.WinForms
{
    partial class MainForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.grpEmployeeInput = new GroupBox();
            this.lblName = new Label();
            this.txtName = new TextBox();
            this.lblHireDate = new Label();
            this.dtpHireDate = new DateTimePicker();
            this.lblSalary = new Label();
            this.txtSalary = new TextBox();
            this.btnAddEmployee = new Button();
            this.grpDepartmentInput = new GroupBox();
            this.lblDeptName = new Label();
            this.txtDeptName = new TextBox();
            this.lblDeptDescription = new Label();
            this.txtDeptDescription = new TextBox();
            this.lblDeptManager = new Label();
            this.cmbDeptManager = new ComboBox();
            this.lblDeptBudget = new Label();
            this.txtDeptBudget = new TextBox();
            this.btnAddDepartment = new Button();
            this.grpActions = new GroupBox();
            this.btnViewEmployees = new Button();
            this.btnViewDepartments = new Button();
            this.btnTestConnection = new Button();
            this.btnTestPLSQL = new Button();
            this.tabControl = new TabControl();
            this.tabEmployees = new TabPage();
            this.dgvEmployees = new DataGridView();
            this.tabDepartments = new TabPage();
            this.dgvDepartments = new DataGridView();
            this.grpLogs = new GroupBox();
            this.dgvLogs = new DataGridView();
            this.btnViewLogs = new Button();
            this.btnViewDeptLogs = new Button();
            this.lblStatus = new Label();
            this.grpEmployeeInput.SuspendLayout();
            this.grpDepartmentInput.SuspendLayout();
            this.grpActions.SuspendLayout();
            this.tabControl.SuspendLayout();
            this.tabEmployees.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvEmployees)).BeginInit();
            this.tabDepartments.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvDepartments)).BeginInit();
            this.grpLogs.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvLogs)).BeginInit();
            this.SuspendLayout();
            //
            // lblTitle
            //
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Microsoft Sans Serif", 16F, FontStyle.Bold, GraphicsUnit.Point);
            this.lblTitle.Location = new Point(12, 9);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(285, 26);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "HR Management System";
            //
            // grpEmployeeInput
            //
            this.grpEmployeeInput.Controls.Add(this.btnAddEmployee);
            this.grpEmployeeInput.Controls.Add(this.txtSalary);
            this.grpEmployeeInput.Controls.Add(this.lblSalary);
            this.grpEmployeeInput.Controls.Add(this.dtpHireDate);
            this.grpEmployeeInput.Controls.Add(this.lblHireDate);
            this.grpEmployeeInput.Controls.Add(this.txtName);
            this.grpEmployeeInput.Controls.Add(this.lblName);
            this.grpEmployeeInput.Location = new Point(12, 50);
            this.grpEmployeeInput.Name = "grpEmployeeInput";
            this.grpEmployeeInput.Size = new Size(400, 180);
            this.grpEmployeeInput.TabIndex = 1;
            this.grpEmployeeInput.TabStop = false;
            this.grpEmployeeInput.Text = "Add New Employee";
            //
            // lblName
            //
            this.lblName.AutoSize = true;
            this.lblName.Location = new Point(15, 30);
            this.lblName.Name = "lblName";
            this.lblName.Size = new Size(42, 15);
            this.lblName.TabIndex = 0;
            this.lblName.Text = "Name:";
            //
            // txtName
            //
            this.txtName.Location = new Point(15, 48);
            this.txtName.MaxLength = 100;
            this.txtName.Name = "txtName";
            this.txtName.Size = new Size(360, 23);
            this.txtName.TabIndex = 1;
            //
            // lblHireDate
            //
            this.lblHireDate.AutoSize = true;
            this.lblHireDate.Location = new Point(15, 80);
            this.lblHireDate.Name = "lblHireDate";
            this.lblHireDate.Size = new Size(61, 15);
            this.lblHireDate.TabIndex = 2;
            this.lblHireDate.Text = "Hire Date:";
            //
            // dtpHireDate
            //
            this.dtpHireDate.Format = DateTimePickerFormat.Short;
            this.dtpHireDate.Location = new Point(15, 98);
            this.dtpHireDate.Name = "dtpHireDate";
            this.dtpHireDate.Size = new Size(200, 23);
            this.dtpHireDate.TabIndex = 3;
            //
            // lblSalary
            //
            this.lblSalary.AutoSize = true;
            this.lblSalary.Location = new Point(230, 80);
            this.lblSalary.Name = "lblSalary";
            this.lblSalary.Size = new Size(41, 15);
            this.lblSalary.TabIndex = 4;
            this.lblSalary.Text = "Salary:";
            //
            // txtSalary
            //
            this.txtSalary.Location = new Point(230, 98);
            this.txtSalary.Name = "txtSalary";
            this.txtSalary.Size = new Size(145, 23);
            this.txtSalary.TabIndex = 5;
            //
            // btnAddEmployee
            //
            this.btnAddEmployee.BackColor = Color.LightGreen;
            this.btnAddEmployee.Font = new Font("Microsoft Sans Serif", 10F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnAddEmployee.Location = new Point(15, 135);
            this.btnAddEmployee.Name = "btnAddEmployee";
            this.btnAddEmployee.Size = new Size(120, 35);
            this.btnAddEmployee.TabIndex = 6;
            this.btnAddEmployee.Text = "Add Employee";
            this.btnAddEmployee.UseVisualStyleBackColor = false;
            this.btnAddEmployee.Click += new EventHandler(this.btnAddEmployee_Click);
            //
            // grpDepartmentInput
            //
            this.grpDepartmentInput.Controls.Add(this.btnAddDepartment);
            this.grpDepartmentInput.Controls.Add(this.txtDeptBudget);
            this.grpDepartmentInput.Controls.Add(this.lblDeptBudget);
            this.grpDepartmentInput.Controls.Add(this.cmbDeptManager);
            this.grpDepartmentInput.Controls.Add(this.lblDeptManager);
            this.grpDepartmentInput.Controls.Add(this.txtDeptDescription);
            this.grpDepartmentInput.Controls.Add(this.lblDeptDescription);
            this.grpDepartmentInput.Controls.Add(this.txtDeptName);
            this.grpDepartmentInput.Controls.Add(this.lblDeptName);
            this.grpDepartmentInput.Location = new Point(12, 250);
            this.grpDepartmentInput.Name = "grpDepartmentInput";
            this.grpDepartmentInput.Size = new Size(400, 220);
            this.grpDepartmentInput.TabIndex = 2;
            this.grpDepartmentInput.TabStop = false;
            this.grpDepartmentInput.Text = "Add New Department";
            //
            // lblDeptName
            //
            this.lblDeptName.AutoSize = true;
            this.lblDeptName.Location = new Point(15, 30);
            this.lblDeptName.Name = "lblDeptName";
            this.lblDeptName.Size = new Size(42, 15);
            this.lblDeptName.TabIndex = 0;
            this.lblDeptName.Text = "Name:";
            //
            // txtDeptName
            //
            this.txtDeptName.Location = new Point(15, 48);
            this.txtDeptName.MaxLength = 100;
            this.txtDeptName.Name = "txtDeptName";
            this.txtDeptName.Size = new Size(360, 23);
            this.txtDeptName.TabIndex = 1;
            //
            // lblDeptDescription
            //
            this.lblDeptDescription.AutoSize = true;
            this.lblDeptDescription.Location = new Point(15, 80);
            this.lblDeptDescription.Name = "lblDeptDescription";
            this.lblDeptDescription.Size = new Size(70, 15);
            this.lblDeptDescription.TabIndex = 2;
            this.lblDeptDescription.Text = "Description:";
            //
            // txtDeptDescription
            //
            this.txtDeptDescription.Location = new Point(15, 98);
            this.txtDeptDescription.MaxLength = 500;
            this.txtDeptDescription.Multiline = true;
            this.txtDeptDescription.Name = "txtDeptDescription";
            this.txtDeptDescription.Size = new Size(360, 40);
            this.txtDeptDescription.TabIndex = 3;
            //
            // lblDeptManager
            //
            this.lblDeptManager.AutoSize = true;
            this.lblDeptManager.Location = new Point(15, 150);
            this.lblDeptManager.Name = "lblDeptManager";
            this.lblDeptManager.Size = new Size(56, 15);
            this.lblDeptManager.TabIndex = 4;
            this.lblDeptManager.Text = "Manager:";
            //
            // cmbDeptManager
            //
            this.cmbDeptManager.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbDeptManager.FormattingEnabled = true;
            this.cmbDeptManager.Location = new Point(15, 168);
            this.cmbDeptManager.Name = "cmbDeptManager";
            this.cmbDeptManager.Size = new Size(200, 23);
            this.cmbDeptManager.TabIndex = 5;
            //
            // lblDeptBudget
            //
            this.lblDeptBudget.AutoSize = true;
            this.lblDeptBudget.Location = new Point(230, 150);
            this.lblDeptBudget.Name = "lblDeptBudget";
            this.lblDeptBudget.Size = new Size(49, 15);
            this.lblDeptBudget.TabIndex = 6;
            this.lblDeptBudget.Text = "Budget:";
            //
            // txtDeptBudget
            //
            this.txtDeptBudget.Location = new Point(230, 168);
            this.txtDeptBudget.Name = "txtDeptBudget";
            this.txtDeptBudget.Size = new Size(145, 23);
            this.txtDeptBudget.TabIndex = 7;
            //
            // btnAddDepartment
            //
            this.btnAddDepartment.BackColor = Color.LightBlue;
            this.btnAddDepartment.Font = new Font("Microsoft Sans Serif", 10F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnAddDepartment.Location = new Point(15, 200);
            this.btnAddDepartment.Name = "btnAddDepartment";
            this.btnAddDepartment.Size = new Size(140, 35);
            this.btnAddDepartment.TabIndex = 8;
            this.btnAddDepartment.Text = "Add Department";
            this.btnAddDepartment.UseVisualStyleBackColor = false;
            this.btnAddDepartment.Click += new EventHandler(this.btnAddDepartment_Click);
            //
            // grpActions
            //
            this.grpActions.Controls.Add(this.btnTestPLSQL);
            this.grpActions.Controls.Add(this.btnTestConnection);
            this.grpActions.Controls.Add(this.btnViewDepartments);
            this.grpActions.Controls.Add(this.btnViewEmployees);
            this.grpActions.Location = new Point(430, 50);
            this.grpActions.Name = "grpActions";
            this.grpActions.Size = new Size(350, 180);
            this.grpActions.TabIndex = 2;
            this.grpActions.TabStop = false;
            this.grpActions.Text = "Actions";
            //
            // btnViewEmployees
            //
            this.btnViewEmployees.BackColor = Color.LightBlue;
            this.btnViewEmployees.Font = new Font("Microsoft Sans Serif", 10F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnViewEmployees.Location = new Point(15, 30);
            this.btnViewEmployees.Name = "btnViewEmployees";
            this.btnViewEmployees.Size = new Size(130, 35);
            this.btnViewEmployees.TabIndex = 0;
            this.btnViewEmployees.Text = "View Employees";
            this.btnViewEmployees.UseVisualStyleBackColor = false;
            this.btnViewEmployees.Click += new EventHandler(this.btnViewEmployees_Click);
            //
            // btnViewDepartments
            //
            this.btnViewDepartments.BackColor = Color.LightCyan;
            this.btnViewDepartments.Font = new Font("Microsoft Sans Serif", 10F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnViewDepartments.Location = new Point(160, 30);
            this.btnViewDepartments.Name = "btnViewDepartments";
            this.btnViewDepartments.Size = new Size(130, 35);
            this.btnViewDepartments.TabIndex = 3;
            this.btnViewDepartments.Text = "View Departments";
            this.btnViewDepartments.UseVisualStyleBackColor = false;
            this.btnViewDepartments.Click += new EventHandler(this.btnViewDepartments_Click);
            //
            // btnTestConnection
            //
            this.btnTestConnection.BackColor = Color.LightYellow;
            this.btnTestConnection.Font = new Font("Microsoft Sans Serif", 10F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnTestConnection.Location = new Point(15, 80);
            this.btnTestConnection.Name = "btnTestConnection";
            this.btnTestConnection.Size = new Size(130, 35);
            this.btnTestConnection.TabIndex = 1;
            this.btnTestConnection.Text = "Test Connection";
            this.btnTestConnection.UseVisualStyleBackColor = false;
            this.btnTestConnection.Click += new EventHandler(this.btnTestConnection_Click);
            //
            // btnTestPLSQL
            //
            this.btnTestPLSQL.BackColor = Color.LightCoral;
            this.btnTestPLSQL.Font = new Font("Microsoft Sans Serif", 10F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnTestPLSQL.Location = new Point(15, 130);
            this.btnTestPLSQL.Name = "btnTestPLSQL";
            this.btnTestPLSQL.Size = new Size(130, 35);
            this.btnTestPLSQL.TabIndex = 2;
            this.btnTestPLSQL.Text = "Test PL/SQL";
            this.btnTestPLSQL.UseVisualStyleBackColor = false;
            this.btnTestPLSQL.Click += new EventHandler(this.btnTestPLSQL_Click);
            //
            // tabControl
            //
            this.tabControl.Controls.Add(this.tabEmployees);
            this.tabControl.Controls.Add(this.tabDepartments);
            this.tabControl.Location = new Point(12, 490);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new Size(768, 200);
            this.tabControl.TabIndex = 4;
            //
            // tabEmployees
            //
            this.tabEmployees.Controls.Add(this.dgvEmployees);
            this.tabEmployees.Location = new Point(4, 24);
            this.tabEmployees.Name = "tabEmployees";
            this.tabEmployees.Padding = new Padding(3);
            this.tabEmployees.Size = new Size(760, 172);
            this.tabEmployees.TabIndex = 0;
            this.tabEmployees.Text = "Employees";
            this.tabEmployees.UseVisualStyleBackColor = true;
            //
            // dgvEmployees
            //
            this.dgvEmployees.AllowUserToAddRows = false;
            this.dgvEmployees.AllowUserToDeleteRows = false;
            this.dgvEmployees.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvEmployees.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvEmployees.Dock = DockStyle.Fill;
            this.dgvEmployees.Location = new Point(3, 3);
            this.dgvEmployees.Name = "dgvEmployees";
            this.dgvEmployees.ReadOnly = true;
            this.dgvEmployees.RowTemplate.Height = 25;
            this.dgvEmployees.Size = new Size(754, 166);
            this.dgvEmployees.TabIndex = 0;
            //
            // tabDepartments
            //
            this.tabDepartments.Controls.Add(this.dgvDepartments);
            this.tabDepartments.Location = new Point(4, 24);
            this.tabDepartments.Name = "tabDepartments";
            this.tabDepartments.Padding = new Padding(3);
            this.tabDepartments.Size = new Size(760, 172);
            this.tabDepartments.TabIndex = 1;
            this.tabDepartments.Text = "Departments";
            this.tabDepartments.UseVisualStyleBackColor = true;
            //
            // dgvDepartments
            //
            this.dgvDepartments.AllowUserToAddRows = false;
            this.dgvDepartments.AllowUserToDeleteRows = false;
            this.dgvDepartments.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvDepartments.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvDepartments.Dock = DockStyle.Fill;
            this.dgvDepartments.Location = new Point(3, 3);
            this.dgvDepartments.Name = "dgvDepartments";
            this.dgvDepartments.ReadOnly = true;
            this.dgvDepartments.RowTemplate.Height = 25;
            this.dgvDepartments.Size = new Size(754, 166);
            this.dgvDepartments.TabIndex = 0;
            //
            // grpLogs
            //
            this.grpLogs.Controls.Add(this.btnViewDeptLogs);
            this.grpLogs.Controls.Add(this.btnViewLogs);
            this.grpLogs.Controls.Add(this.dgvLogs);
            this.grpLogs.Location = new Point(12, 710);
            this.grpLogs.Name = "grpLogs";
            this.grpLogs.Size = new Size(768, 200);
            this.grpLogs.TabIndex = 5;
            this.grpLogs.TabStop = false;
            this.grpLogs.Text = "Audit Logs";
            //
            // dgvLogs
            //
            this.dgvLogs.AllowUserToAddRows = false;
            this.dgvLogs.AllowUserToDeleteRows = false;
            this.dgvLogs.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvLogs.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvLogs.Location = new Point(15, 50);
            this.dgvLogs.Name = "dgvLogs";
            this.dgvLogs.ReadOnly = true;
            this.dgvLogs.RowTemplate.Height = 25;
            this.dgvLogs.Size = new Size(738, 135);
            this.dgvLogs.TabIndex = 0;
            //
            // btnViewLogs
            //
            this.btnViewLogs.BackColor = Color.LightGray;
            this.btnViewLogs.Font = new Font("Microsoft Sans Serif", 9F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnViewLogs.Location = new Point(15, 20);
            this.btnViewLogs.Name = "btnViewLogs";
            this.btnViewLogs.Size = new Size(100, 25);
            this.btnViewLogs.TabIndex = 1;
            this.btnViewLogs.Text = "View Logs";
            this.btnViewLogs.UseVisualStyleBackColor = false;
            this.btnViewLogs.Click += new EventHandler(this.btnViewLogs_Click);
            //
            // btnViewDeptLogs
            //
            this.btnViewDeptLogs.BackColor = Color.LightGray;
            this.btnViewDeptLogs.Font = new Font("Microsoft Sans Serif", 9F, FontStyle.Bold, GraphicsUnit.Point);
            this.btnViewDeptLogs.Location = new Point(125, 20);
            this.btnViewDeptLogs.Name = "btnViewDeptLogs";
            this.btnViewDeptLogs.Size = new Size(120, 25);
            this.btnViewDeptLogs.TabIndex = 2;
            this.btnViewDeptLogs.Text = "View Dept Logs";
            this.btnViewDeptLogs.UseVisualStyleBackColor = false;
            this.btnViewDeptLogs.Click += new EventHandler(this.btnViewDeptLogs_Click);
            //
            // lblStatus
            //
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new Font("Microsoft Sans Serif", 9F, FontStyle.Regular, GraphicsUnit.Point);
            this.lblStatus.Location = new Point(12, 925);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(41, 15);
            this.lblStatus.TabIndex = 6;
            this.lblStatus.Text = "Ready";
            //
            // MainForm
            //
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 960);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.grpLogs);
            this.Controls.Add(this.tabControl);
            this.Controls.Add(this.grpActions);
            this.Controls.Add(this.grpDepartmentInput);
            this.Controls.Add(this.grpEmployeeInput);
            this.Controls.Add(this.lblTitle);
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "MainForm";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "MyHRM - HR Management System";
            this.Load += new EventHandler(this.MainForm_Load);
            this.grpEmployeeInput.ResumeLayout(false);
            this.grpEmployeeInput.PerformLayout();
            this.grpDepartmentInput.ResumeLayout(false);
            this.grpDepartmentInput.PerformLayout();
            this.grpActions.ResumeLayout(false);
            this.tabControl.ResumeLayout(false);
            this.tabEmployees.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvEmployees)).EndInit();
            this.tabDepartments.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvDepartments)).EndInit();
            this.grpLogs.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvLogs)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private Label lblTitle;
        private GroupBox grpEmployeeInput;
        private Label lblName;
        private TextBox txtName;
        private Label lblHireDate;
        private DateTimePicker dtpHireDate;
        private Label lblSalary;
        private TextBox txtSalary;
        private Button btnAddEmployee;
        private GroupBox grpDepartmentInput;
        private Label lblDeptName;
        private TextBox txtDeptName;
        private Label lblDeptDescription;
        private TextBox txtDeptDescription;
        private Label lblDeptManager;
        private ComboBox cmbDeptManager;
        private Label lblDeptBudget;
        private TextBox txtDeptBudget;
        private Button btnAddDepartment;
        private GroupBox grpActions;
        private Button btnViewEmployees;
        private Button btnViewDepartments;
        private Button btnTestConnection;
        private Button btnTestPLSQL;
        private TabControl tabControl;
        private TabPage tabEmployees;
        private DataGridView dgvEmployees;
        private TabPage tabDepartments;
        private DataGridView dgvDepartments;
        private GroupBox grpLogs;
        private DataGridView dgvLogs;
        private Button btnViewLogs;
        private Button btnViewDeptLogs;
        private Label lblStatus;
    }
}
