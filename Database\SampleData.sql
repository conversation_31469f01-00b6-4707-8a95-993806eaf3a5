-- =====================================================
-- MyHRM - Sample Data Script
-- Oracle 11g Sample Data for Testing
-- =====================================================

-- Enable DBMS_OUTPUT for feedback
SET SERVEROUTPUT ON;

BEGIN
    DBMS_OUTPUT.PUT_LINE('=== Loading Sample Data for MyHRM ===');
    DBMS_OUTPUT.PUT_LINE('Date: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'));
END;
/

-- =====================================================
-- 1. INSERT SAMPLE EMPLOYEES USING PACKAGE PROCEDURE
-- =====================================================

DECLARE
    v_employee_id NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Inserting sample employees...');

    -- Employee 1: John Smith
    emp_pkg.add_employee(
        p_name => 'John Smith',
        p_hire_date => DATE '2020-01-15',
        p_salary => 75000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added John Smith (ID: ' || v_employee_id || ')');

    -- Employee 2: Sarah Johnson
    emp_pkg.add_employee(
        p_name => 'Sarah Johnson',
        p_hire_date => DATE '2019-03-22',
        p_salary => 82000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Sarah Johnson (ID: ' || v_employee_id || ')');

    -- Employee 3: Michael Brown
    emp_pkg.add_employee(
        p_name => 'Michael Brown',
        p_hire_date => DATE '2021-07-10',
        p_salary => 65000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Michael Brown (ID: ' || v_employee_id || ')');

    -- Employee 4: Emily Davis
    emp_pkg.add_employee(
        p_name => 'Emily Davis',
        p_hire_date => DATE '2018-11-05',
        p_salary => 95000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Emily Davis (ID: ' || v_employee_id || ')');

    -- Employee 5: David Wilson
    emp_pkg.add_employee(
        p_name => 'David Wilson',
        p_hire_date => DATE '2022-02-28',
        p_salary => 58000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added David Wilson (ID: ' || v_employee_id || ')');

    -- Employee 6: Lisa Anderson
    emp_pkg.add_employee(
        p_name => 'Lisa Anderson',
        p_hire_date => DATE '2020-09-14',
        p_salary => 71000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Lisa Anderson (ID: ' || v_employee_id || ')');

    -- Employee 7: Robert Taylor
    emp_pkg.add_employee(
        p_name => 'Robert Taylor',
        p_hire_date => DATE '2017-05-30',
        p_salary => 88000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Robert Taylor (ID: ' || v_employee_id || ')');

    -- Employee 8: Jennifer Martinez
    emp_pkg.add_employee(
        p_name => 'Jennifer Martinez',
        p_hire_date => DATE '2021-12-01',
        p_salary => 62000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Jennifer Martinez (ID: ' || v_employee_id || ')');

    -- Employee 9: Christopher Lee
    emp_pkg.add_employee(
        p_name => 'Christopher Lee',
        p_hire_date => DATE '2019-08-18',
        p_salary => 79000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Christopher Lee (ID: ' || v_employee_id || ')');

    -- Employee 10: Amanda White
    emp_pkg.add_employee(
        p_name => 'Amanda White',
        p_hire_date => DATE '2023-01-09',
        p_salary => 55000.00,
        p_employee_id => v_employee_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Amanda White (ID: ' || v_employee_id || ')');

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error inserting sample data: ' || SQLERRM);
        ROLLBACK;
        RAISE;
END;
/

-- =====================================================
-- 2. INSERT SAMPLE DEPARTMENTS USING PACKAGE PROCEDURE
-- =====================================================

DECLARE
    v_department_id NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Inserting sample departments...');

    -- Department 1: Information Technology
    dept_pkg.add_department(
        p_name => 'Information Technology',
        p_description => 'Responsible for all IT infrastructure, software development, and technical support',
        p_manager_id => 4, -- Emily Davis
        p_budget => 500000.00,
        p_department_id => v_department_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Information Technology (ID: ' || v_department_id || ')');

    -- Department 2: Human Resources
    dept_pkg.add_department(
        p_name => 'Human Resources',
        p_description => 'Manages employee relations, recruitment, and organizational development',
        p_manager_id => 2, -- Sarah Johnson
        p_budget => 250000.00,
        p_department_id => v_department_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Human Resources (ID: ' || v_department_id || ')');

    -- Department 3: Finance
    dept_pkg.add_department(
        p_name => 'Finance',
        p_description => 'Handles financial planning, accounting, and budget management',
        p_manager_id => 7, -- Robert Taylor
        p_budget => 300000.00,
        p_department_id => v_department_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Finance (ID: ' || v_department_id || ')');

    -- Department 4: Marketing
    dept_pkg.add_department(
        p_name => 'Marketing',
        p_description => 'Develops marketing strategies and manages brand communications',
        p_manager_id => 1, -- John Smith
        p_budget => 200000.00,
        p_department_id => v_department_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Marketing (ID: ' || v_department_id || ')');

    -- Department 5: Operations
    dept_pkg.add_department(
        p_name => 'Operations',
        p_description => 'Oversees daily business operations and process optimization',
        p_manager_id => 9, -- Christopher Lee
        p_budget => 400000.00,
        p_department_id => v_department_id
    );
    DBMS_OUTPUT.PUT_LINE('✓ Added Operations (ID: ' || v_department_id || ')');

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error inserting department data: ' || SQLERRM);
        ROLLBACK;
        RAISE;
END;
/

-- =====================================================
-- 3. ASSIGN EMPLOYEES TO DEPARTMENTS
-- =====================================================

DECLARE
    v_count NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Assigning employees to departments...');

    -- Assign employees to departments
    UPDATE employees SET department_id = 1 WHERE id IN (1, 3, 4, 5);  -- IT Department
    UPDATE employees SET department_id = 2 WHERE id IN (2, 6);        -- HR Department
    UPDATE employees SET department_id = 3 WHERE id IN (7, 8);        -- Finance Department
    UPDATE employees SET department_id = 4 WHERE id = 10;             -- Marketing Department
    UPDATE employees SET department_id = 5 WHERE id = 9;              -- Operations Department

    SELECT COUNT(*) INTO v_count FROM employees WHERE department_id IS NOT NULL;
    DBMS_OUTPUT.PUT_LINE('✓ Assigned ' || v_count || ' employees to departments');

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error assigning employees to departments: ' || SQLERRM);
        ROLLBACK;
        RAISE;
END;
/

-- =====================================================
-- 4. DEMONSTRATE SALARY UPDATES (TRIGGERS)
-- =====================================================

DECLARE
    v_employee_id NUMBER;
    v_old_salary NUMBER;
    v_new_salary NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Demonstrating salary updates...');

    -- Get first employee for salary update demonstration
    SELECT id, salary INTO v_employee_id, v_old_salary
    FROM employees
    WHERE ROWNUM = 1;

    v_new_salary := v_old_salary + 5000;

    -- Update salary using package procedure (will trigger audit log)
    emp_pkg.update_employee_salary(v_employee_id, v_new_salary);

    DBMS_OUTPUT.PUT_LINE('✓ Updated employee ' || v_employee_id ||
                        ' salary from $' || v_old_salary || ' to $' || v_new_salary);

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error updating salary: ' || SQLERRM);
END;
/

-- =====================================================
-- 5. DEMONSTRATE DEPARTMENT UPDATES
-- =====================================================

DECLARE
    v_department_id NUMBER := 1;
    v_old_budget NUMBER;
    v_new_budget NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Demonstrating department budget updates...');

    -- Get current budget for IT department
    SELECT budget INTO v_old_budget
    FROM departments
    WHERE id = v_department_id;

    v_new_budget := v_old_budget + 50000;

    -- Update budget using package procedure (will trigger audit log)
    dept_pkg.update_department(
        p_department_id => v_department_id,
        p_budget => v_new_budget
    );

    DBMS_OUTPUT.PUT_LINE('✓ Updated department ' || v_department_id ||
                        ' budget from $' || TO_CHAR(v_old_budget, '999,999.99') ||
                        ' to $' || TO_CHAR(v_new_budget, '999,999.99'));

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error updating department budget: ' || SQLERRM);
END;
/

-- =====================================================
-- 6. DISPLAY SAMPLE DATA SUMMARY
-- =====================================================

DECLARE
    v_total_employees NUMBER;
    v_avg_salary NUMBER(10,2);
    v_min_salary NUMBER(10,2);
    v_max_salary NUMBER(10,2);
    v_total_logs NUMBER;
    v_total_departments NUMBER;
    v_total_dept_logs NUMBER;
    v_total_budget NUMBER(12,2);
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== SAMPLE DATA SUMMARY ===');

    -- Get employee statistics
    SELECT COUNT(*), AVG(salary), MIN(salary), MAX(salary)
    INTO v_total_employees, v_avg_salary, v_min_salary, v_max_salary
    FROM employees;

    -- Get department statistics
    SELECT COUNT(*), NVL(SUM(budget), 0)
    INTO v_total_departments, v_total_budget
    FROM departments;

    -- Get log counts
    SELECT COUNT(*) INTO v_total_logs FROM employee_log;
    SELECT COUNT(*) INTO v_total_dept_logs FROM department_log;

    DBMS_OUTPUT.PUT_LINE('Total Employees: ' || v_total_employees);
    DBMS_OUTPUT.PUT_LINE('Total Departments: ' || v_total_departments);
    DBMS_OUTPUT.PUT_LINE('Average Salary: $' || TO_CHAR(v_avg_salary, '999,999.99'));
    DBMS_OUTPUT.PUT_LINE('Salary Range: $' || TO_CHAR(v_min_salary, '999,999.99') ||
                        ' - $' || TO_CHAR(v_max_salary, '999,999.99'));
    DBMS_OUTPUT.PUT_LINE('Total Budget: $' || TO_CHAR(v_total_budget, '999,999,999.99'));
    DBMS_OUTPUT.PUT_LINE('Employee Audit Logs: ' || v_total_logs);
    DBMS_OUTPUT.PUT_LINE('Department Audit Logs: ' || v_total_dept_logs);

    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Department List:');

    FOR dept_rec IN (SELECT d.id, d.name, d.budget, e.name as manager_name,
                            (SELECT COUNT(*) FROM employees WHERE department_id = d.id) as emp_count
                     FROM departments d
                     LEFT JOIN employees e ON d.manager_id = e.id
                     ORDER BY d.id) LOOP
        DBMS_OUTPUT.PUT_LINE('  ' || dept_rec.id || '. ' || dept_rec.name ||
                           ' - Budget: $' || TO_CHAR(dept_rec.budget, '999,999.99') ||
                           ' - Manager: ' || NVL(dept_rec.manager_name, 'None') ||
                           ' - Employees: ' || dept_rec.emp_count);
    END LOOP;

    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Employee List:');

    FOR emp_rec IN (SELECT e.id, e.name, e.salary, e.hire_date, d.name as dept_name
                    FROM employees e
                    LEFT JOIN departments d ON e.department_id = d.id
                    ORDER BY e.id) LOOP
        DBMS_OUTPUT.PUT_LINE('  ' || emp_rec.id || '. ' || emp_rec.name ||
                           ' - $' || TO_CHAR(emp_rec.salary, '999,999.99') ||
                           ' - Dept: ' || NVL(emp_rec.dept_name, 'Unassigned') ||
                           ' (Hired: ' || TO_CHAR(emp_rec.hire_date, 'YYYY-MM-DD') || ')');
    END LOOP;

END;
/

-- =====================================================
-- 7. TEST PL/SQL FUNCTIONS
-- =====================================================

DECLARE
    v_employee_id NUMBER := 1;
    v_department_id NUMBER := 1;
    v_salary NUMBER(10,2);
    v_bonus NUMBER(10,2);
    v_dept_name VARCHAR2(100);
    v_dept_count NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== TESTING PL/SQL FUNCTIONS ===');

    -- Test employee functions
    v_salary := emp_pkg.get_salary_by_id(v_employee_id);
    DBMS_OUTPUT.PUT_LINE('Employee ' || v_employee_id || ' salary: $' || TO_CHAR(v_salary, '999,999.99'));

    v_bonus := emp_pkg.calculate_annual_bonus(v_employee_id, 15);
    DBMS_OUTPUT.PUT_LINE('Employee ' || v_employee_id || ' annual bonus (15%): $' || TO_CHAR(v_bonus, '999,999.99'));

    DBMS_OUTPUT.PUT_LINE('Total employee count: ' || emp_pkg.get_employee_count);

    -- Test department functions
    v_dept_name := dept_pkg.get_department_by_id(v_department_id);
    DBMS_OUTPUT.PUT_LINE('Department ' || v_department_id || ' name: ' || v_dept_name);

    DBMS_OUTPUT.PUT_LINE('Total department count: ' || dept_pkg.get_department_count);

    v_dept_count := dept_pkg.get_departments_by_manager(4); -- Emily Davis
    DBMS_OUTPUT.PUT_LINE('Departments managed by employee 4: ' || v_dept_count);

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error testing functions: ' || SQLERRM);
END;
/

-- =====================================================
-- 8. RUN COMPREHENSIVE PL/SQL DEMONSTRATION
-- =====================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== RUNNING COMPREHENSIVE DEMONSTRATION ===');
    demo_plsql_features;
END;
/

-- =====================================================
-- 9. COMPLETION MESSAGE
-- =====================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('========================================');
    DBMS_OUTPUT.PUT_LINE('Sample Data Loading Complete!');
    DBMS_OUTPUT.PUT_LINE('========================================');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('The database is now ready for testing with:');
    DBMS_OUTPUT.PUT_LINE('- 10 sample employees assigned to departments');
    DBMS_OUTPUT.PUT_LINE('- 5 sample departments with managers and budgets');
    DBMS_OUTPUT.PUT_LINE('- Employee and department audit log entries from triggers');
    DBMS_OUTPUT.PUT_LINE('- All PL/SQL features demonstrated (emp_pkg and dept_pkg)');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('You can now run the C# WinForms application!');
    DBMS_OUTPUT.PUT_LINE('========================================');
END;
/

-- Optional: Display final data verification
SELECT 'EMPLOYEES' as table_name, COUNT(*) as record_count FROM employees
UNION ALL
SELECT 'DEPARTMENTS' as table_name, COUNT(*) as record_count FROM departments
UNION ALL
SELECT 'EMPLOYEE_LOG' as table_name, COUNT(*) as record_count FROM employee_log
UNION ALL
SELECT 'DEPARTMENT_LOG' as table_name, COUNT(*) as record_count FROM department_log
ORDER BY table_name;

-- Show recent employee audit log entries
SELECT
    'EMPLOYEE' as log_type,
    log_id,
    employee_id as entity_id,
    action,
    TO_CHAR(log_date, 'YYYY-MM-DD HH24:MI:SS') as log_date,
    TO_CHAR(old_salary, '999,999.99') as old_value,
    TO_CHAR(new_salary, '999,999.99') as new_value
FROM employee_log
ORDER BY log_date DESC, log_id DESC;

-- Show recent department audit log entries
SELECT
    'DEPARTMENT' as log_type,
    log_id,
    department_id as entity_id,
    action,
    TO_CHAR(log_date, 'YYYY-MM-DD HH24:MI:SS') as log_date,
    TO_CHAR(old_budget, '999,999.99') as old_budget,
    TO_CHAR(new_budget, '999,999.99') as new_budget,
    old_manager_id,
    new_manager_id
FROM department_log
ORDER BY log_date DESC, log_id DESC;
