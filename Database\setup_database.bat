@echo off
echo ========================================
echo MyHRM Database Setup Script
echo ========================================
echo.
echo This script will create the database schema
echo and load sample data for the MyHRM system.
echo.
echo Prerequisites:
echo - Oracle 11g Express Edition or later installed and running
echo - SQL*Plus available in PATH
echo - Database user with CREATE privileges
echo.
echo Default connection: hr2/1234@************:1521/ORCL
echo.

set /p CONTINUE="Continue with database setup? (Y/N): "
if /i "%CONTINUE%" neq "Y" (
    echo Setup cancelled.
    pause
    exit /b 0
)

echo.
echo ========================================
echo Step 1: Creating Database Schema
echo ========================================
echo.

sqlplus "hr2/1234@(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCL)))" @CreateSchema.sql
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Schema creation failed!
    echo Please check:
    echo - Oracle database is running
    echo - Connection parameters are correct
    echo - User has sufficient privileges
    pause
    exit /b 1
)

echo.
echo ========================================
echo Step 2: Loading Sample Data
echo ========================================
echo.

sqlplus "hr2/1234@(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCL)))" @SampleData.sql
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Sample data loading failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Database Setup Complete!
echo ========================================
echo.
echo The following objects have been created:
echo - Tables: employees, employee_log, departments, department_log
echo - Sequences: emp_seq, log_seq, dept_seq, dept_log_seq
echo - Packages: emp_pkg, dept_pkg
echo - Triggers: trg_employee_audit, trg_employee_validation, trg_department_audit, trg_department_validation
echo - Sample data: 10 employees, 5 departments with audit logs
echo.
echo You can now run the C# WinForms application!
echo ========================================
pause
