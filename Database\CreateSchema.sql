-- =====================================================
-- MyHRM - HR Management System Database Schema
-- Oracle 11g Database Script with Advanced PL/SQL
-- =====================================================

-- Clean up existing objects (for re-running script)
BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE emp_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE log_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE dept_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP SEQUENCE dept_log_seq';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE employee_log';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE department_log';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE employees';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE departments';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP PACKAGE emp_pkg';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP PACKAGE dept_pkg';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- =====================================================
-- 1. CREATE TABLES
-- =====================================================

-- Departments table
CREATE TABLE departments (
    id NUMBER PRIMARY KEY,
    name VARCHAR2(100) NOT NULL UNIQUE,
    description VARCHAR2(500),
    manager_id NUMBER,
    budget NUMBER(12,2) CHECK (budget >= 0),
    created_date DATE DEFAULT SYSDATE
);

-- Main employees table
CREATE TABLE employees (
    id NUMBER PRIMARY KEY,
    name VARCHAR2(100) NOT NULL,
    hire_date DATE NOT NULL,
    salary NUMBER(10,2) NOT NULL CHECK (salary >= 0),
    department_id NUMBER,
    created_date DATE DEFAULT SYSDATE,
    CONSTRAINT fk_emp_department FOREIGN KEY (department_id) REFERENCES departments(id)
);

-- Employee audit log table
CREATE TABLE employee_log (
    log_id NUMBER PRIMARY KEY,
    employee_id NUMBER NOT NULL,
    action VARCHAR2(50) NOT NULL,
    log_date DATE DEFAULT SYSDATE,
    old_salary NUMBER(10,2),
    new_salary NUMBER(10,2)
);

-- Department audit log table
CREATE TABLE department_log (
    log_id NUMBER PRIMARY KEY,
    department_id NUMBER NOT NULL,
    action VARCHAR2(50) NOT NULL,
    log_date DATE DEFAULT SYSDATE,
    old_budget NUMBER(12,2),
    new_budget NUMBER(12,2),
    old_manager_id NUMBER,
    new_manager_id NUMBER
);

-- =====================================================
-- 2. CREATE SEQUENCES
-- =====================================================

-- Sequence for employee IDs (auto-increment)
CREATE SEQUENCE emp_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- Sequence for log IDs
CREATE SEQUENCE log_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- Sequence for department IDs
CREATE SEQUENCE dept_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- Sequence for department log IDs
CREATE SEQUENCE dept_log_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- =====================================================
-- 3. PL/SQL VARIABLES DEMONSTRATION
-- =====================================================

-- Anonymous block demonstrating variables
DECLARE
    -- Variable declarations
    v_employee_count NUMBER := 0;
    v_avg_salary NUMBER(10,2);
    v_message VARCHAR2(200);
    v_current_date DATE := SYSDATE;
BEGIN
    -- Using variables to store and manipulate data
    SELECT COUNT(*) INTO v_employee_count FROM employees;

    IF v_employee_count > 0 THEN
        SELECT AVG(salary) INTO v_avg_salary FROM employees;
        v_message := 'Current employee count: ' || v_employee_count ||
                    ', Average salary: $' || TO_CHAR(v_avg_salary, '999,999.99');
    ELSE
        v_message := 'No employees found in the system.';
    END IF;

    DBMS_OUTPUT.PUT_LINE('=== VARIABLES DEMO ===');
    DBMS_OUTPUT.PUT_LINE('Date: ' || TO_CHAR(v_current_date, 'YYYY-MM-DD HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE(v_message);
END;
/

-- =====================================================
-- 4. PL/SQL CURSOR DEMONSTRATION
-- =====================================================

-- Anonymous block demonstrating explicit cursor
DECLARE
    -- Cursor declaration
    CURSOR emp_cursor IS
        SELECT id, name, salary, hire_date
        FROM employees
        WHERE salary > 5000
        ORDER BY salary DESC;

    -- Variables for cursor
    v_emp_record emp_cursor%ROWTYPE;
    v_counter NUMBER := 0;
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== CURSOR DEMO ===');
    DBMS_OUTPUT.PUT_LINE('Employees with salary > $5,000:');

    -- Open and fetch from cursor
    OPEN emp_cursor;
    LOOP
        FETCH emp_cursor INTO v_emp_record;
        EXIT WHEN emp_cursor%NOTFOUND;

        v_counter := v_counter + 1;
        DBMS_OUTPUT.PUT_LINE(v_counter || '. ' || v_emp_record.name ||
                           ' - $' || TO_CHAR(v_emp_record.salary, '999,999.99') ||
                           ' (Hired: ' || TO_CHAR(v_emp_record.hire_date, 'YYYY-MM-DD') || ')');
    END LOOP;
    CLOSE emp_cursor;

    IF v_counter = 0 THEN
        DBMS_OUTPUT.PUT_LINE('No employees found with salary > $5,000');
    END IF;
END;
/

-- =====================================================
-- 5. PL/SQL CONDITIONAL LOGIC DEMONSTRATION
-- =====================================================

-- Anonymous block demonstrating IF statements
DECLARE
    v_employee_id NUMBER;
    v_salary NUMBER(10,2);
    v_performance_rating VARCHAR2(20);
    v_bonus_percentage NUMBER(5,2);
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== CONDITIONAL LOGIC DEMO ===');

    -- Loop through employees and assign performance ratings
    FOR emp_rec IN (SELECT id, name, salary FROM employees) LOOP
        v_employee_id := emp_rec.id;
        v_salary := emp_rec.salary;

        -- Conditional logic using IF statements
        IF v_salary >= 80000 THEN
            v_performance_rating := 'Excellent';
            v_bonus_percentage := 15.0;
        ELSIF v_salary >= 60000 THEN
            v_performance_rating := 'Good';
            v_bonus_percentage := 10.0;
        ELSIF v_salary >= 40000 THEN
            v_performance_rating := 'Average';
            v_bonus_percentage := 5.0;
        ELSE
            v_performance_rating := 'Below Average';
            v_bonus_percentage := 2.0;
        END IF;

        DBMS_OUTPUT.PUT_LINE(emp_rec.name || ' - Rating: ' || v_performance_rating ||
                           ', Bonus: ' || v_bonus_percentage || '%');
    END LOOP;
END;
/

-- =====================================================
-- 6. PL/SQL LOOP DEMONSTRATION
-- =====================================================

-- Anonymous block demonstrating different types of loops
DECLARE
    v_counter NUMBER := 1;
    v_employee_name VARCHAR2(100);
    v_total_employees NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== LOOP DEMO ===');

    -- Get total employee count
    SELECT COUNT(*) INTO v_total_employees FROM employees;

    -- FOR LOOP demonstration
    DBMS_OUTPUT.PUT_LINE('FOR LOOP - Employee List:');
    FOR emp_rec IN (SELECT name, salary FROM employees ORDER BY name) LOOP
        DBMS_OUTPUT.PUT_LINE(v_counter || '. ' || emp_rec.name || ' - $' || emp_rec.salary);
        v_counter := v_counter + 1;
    END LOOP;

    -- WHILE LOOP demonstration
    DBMS_OUTPUT.PUT_LINE('WHILE LOOP - Counting down from 5:');
    v_counter := 5;
    WHILE v_counter > 0 LOOP
        DBMS_OUTPUT.PUT_LINE('Count: ' || v_counter);
        v_counter := v_counter - 1;
    END LOOP;

    -- Basic LOOP with EXIT demonstration
    DBMS_OUTPUT.PUT_LINE('BASIC LOOP - Processing first 3 employees:');
    v_counter := 1;
    LOOP
        SELECT name INTO v_employee_name
        FROM (SELECT name, ROWNUM rn FROM employees ORDER BY id)
        WHERE rn = v_counter;

        DBMS_OUTPUT.PUT_LINE('Employee ' || v_counter || ': ' || v_employee_name);
        v_counter := v_counter + 1;

        EXIT WHEN v_counter > 3 OR v_counter > v_total_employees;
    END LOOP;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        DBMS_OUTPUT.PUT_LINE('No more employees to process.');
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error in loop demonstration: ' || SQLERRM);
END;
/

-- =====================================================
-- 7. PL/SQL PACKAGE SPECIFICATION
-- =====================================================

-- Package specification (header)
CREATE OR REPLACE PACKAGE emp_pkg AS
    -- Public procedure to add employee
    PROCEDURE add_employee(
        p_name IN VARCHAR2,
        p_hire_date IN DATE,
        p_salary IN NUMBER,
        p_employee_id OUT NUMBER
    );

    -- Public function to get salary by employee ID
    FUNCTION get_salary_by_id(p_employee_id IN NUMBER) RETURN NUMBER;

    -- Public function to get employee count
    FUNCTION get_employee_count RETURN NUMBER;

    -- Public procedure to update employee salary
    PROCEDURE update_employee_salary(
        p_employee_id IN NUMBER,
        p_new_salary IN NUMBER
    );

    -- Public function to calculate annual bonus
    FUNCTION calculate_annual_bonus(
        p_employee_id IN NUMBER,
        p_bonus_percentage IN NUMBER DEFAULT 10
    ) RETURN NUMBER;

    -- Exception declarations
    employee_not_found EXCEPTION;
    invalid_salary EXCEPTION;
END emp_pkg;
/

-- =====================================================
-- 8. PL/SQL PACKAGE BODY
-- =====================================================

-- Package body (implementation)
CREATE OR REPLACE PACKAGE BODY emp_pkg AS

    -- Private procedure for logging (not accessible outside package)
    PROCEDURE log_activity(
        p_employee_id IN NUMBER,
        p_action IN VARCHAR2,
        p_old_salary IN NUMBER DEFAULT NULL,
        p_new_salary IN NUMBER DEFAULT NULL
    ) IS
    BEGIN
        INSERT INTO employee_log (log_id, employee_id, action, log_date, old_salary, new_salary)
        VALUES (log_seq.NEXTVAL, p_employee_id, p_action, SYSDATE, p_old_salary, p_new_salary);
        COMMIT;
    END log_activity;

    -- Implementation of add_employee procedure
    PROCEDURE add_employee(
        p_name IN VARCHAR2,
        p_hire_date IN DATE,
        p_salary IN NUMBER,
        p_employee_id OUT NUMBER
    ) IS
        v_emp_id NUMBER;
    BEGIN
        -- Validation
        IF p_name IS NULL OR LENGTH(TRIM(p_name)) = 0 THEN
            RAISE_APPLICATION_ERROR(-20001, 'Employee name cannot be empty');
        END IF;

        IF p_salary < 0 THEN
            RAISE invalid_salary;
        END IF;

        IF p_hire_date > SYSDATE THEN
            RAISE_APPLICATION_ERROR(-20003, 'Hire date cannot be in the future');
        END IF;

        -- Get next employee ID
        SELECT emp_seq.NEXTVAL INTO v_emp_id FROM dual;

        -- Insert employee
        INSERT INTO employees (id, name, hire_date, salary, created_date)
        VALUES (v_emp_id, TRIM(p_name), p_hire_date, p_salary, SYSDATE);

        -- Log the activity
        log_activity(v_emp_id, 'INSERT', NULL, p_salary);

        -- Return the generated ID
        p_employee_id := v_emp_id;

        COMMIT;

    EXCEPTION
        WHEN invalid_salary THEN
            RAISE_APPLICATION_ERROR(-20002, 'Salary cannot be negative');
        WHEN OTHERS THEN
            ROLLBACK;
            RAISE;
    END add_employee;

    -- Implementation of get_salary_by_id function
    FUNCTION get_salary_by_id(p_employee_id IN NUMBER) RETURN NUMBER IS
        v_salary NUMBER(10,2);
    BEGIN
        -- Validation
        IF p_employee_id IS NULL OR p_employee_id <= 0 THEN
            RAISE_APPLICATION_ERROR(-20004, 'Invalid employee ID');
        END IF;

        -- Get salary
        SELECT salary INTO v_salary
        FROM employees
        WHERE id = p_employee_id;

        RETURN v_salary;

    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RAISE employee_not_found;
        WHEN employee_not_found THEN
            RAISE_APPLICATION_ERROR(-20005, 'Employee not found with ID: ' || p_employee_id);
        WHEN OTHERS THEN
            RAISE;
    END get_salary_by_id;

    -- Implementation of get_employee_count function
    FUNCTION get_employee_count RETURN NUMBER IS
        v_count NUMBER;
    BEGIN
        SELECT COUNT(*) INTO v_count FROM employees;
        RETURN v_count;
    END get_employee_count;

    -- Implementation of update_employee_salary procedure
    PROCEDURE update_employee_salary(
        p_employee_id IN NUMBER,
        p_new_salary IN NUMBER
    ) IS
        v_old_salary NUMBER(10,2);
        v_employee_exists NUMBER;
    BEGIN
        -- Check if employee exists and get current salary
        SELECT COUNT(*), NVL(MAX(salary), 0)
        INTO v_employee_exists, v_old_salary
        FROM employees
        WHERE id = p_employee_id;

        IF v_employee_exists = 0 THEN
            RAISE employee_not_found;
        END IF;

        IF p_new_salary < 0 THEN
            RAISE invalid_salary;
        END IF;

        -- Update salary
        UPDATE employees
        SET salary = p_new_salary
        WHERE id = p_employee_id;

        -- Log the activity
        log_activity(p_employee_id, 'UPDATE_SALARY', v_old_salary, p_new_salary);

        COMMIT;

    EXCEPTION
        WHEN employee_not_found THEN
            RAISE_APPLICATION_ERROR(-20005, 'Employee not found with ID: ' || p_employee_id);
        WHEN invalid_salary THEN
            RAISE_APPLICATION_ERROR(-20002, 'Salary cannot be negative');
        WHEN OTHERS THEN
            ROLLBACK;
            RAISE;
    END update_employee_salary;

    -- Implementation of calculate_annual_bonus function
    FUNCTION calculate_annual_bonus(
        p_employee_id IN NUMBER,
        p_bonus_percentage IN NUMBER DEFAULT 10
    ) RETURN NUMBER IS
        v_salary NUMBER(10,2);
        v_bonus NUMBER(10,2);
    BEGIN
        -- Get employee salary
        v_salary := get_salary_by_id(p_employee_id);

        -- Calculate annual bonus
        v_bonus := (v_salary * 12) * (p_bonus_percentage / 100);

        RETURN v_bonus;

    EXCEPTION
        WHEN OTHERS THEN
            RAISE;
    END calculate_annual_bonus;

END emp_pkg;
/

-- =====================================================
-- 8B. DEPARTMENT PL/SQL PACKAGE SPECIFICATION
-- =====================================================

-- Department package specification (header)
CREATE OR REPLACE PACKAGE dept_pkg AS
    -- Public procedure to add department
    PROCEDURE add_department(
        p_name IN VARCHAR2,
        p_description IN VARCHAR2 DEFAULT NULL,
        p_manager_id IN NUMBER DEFAULT NULL,
        p_budget IN NUMBER DEFAULT NULL,
        p_department_id OUT NUMBER
    );

    -- Public function to get department by ID
    FUNCTION get_department_by_id(p_department_id IN NUMBER) RETURN VARCHAR2;

    -- Public function to get department count
    FUNCTION get_department_count RETURN NUMBER;

    -- Public procedure to update department
    PROCEDURE update_department(
        p_department_id IN NUMBER,
        p_name IN VARCHAR2 DEFAULT NULL,
        p_description IN VARCHAR2 DEFAULT NULL,
        p_manager_id IN NUMBER DEFAULT NULL,
        p_budget IN NUMBER DEFAULT NULL
    );

    -- Public function to get departments by manager
    FUNCTION get_departments_by_manager(p_manager_id IN NUMBER) RETURN NUMBER;

    -- Public procedure to assign manager to department
    PROCEDURE assign_manager(
        p_department_id IN NUMBER,
        p_manager_id IN NUMBER
    );

    -- Exception declarations
    department_not_found EXCEPTION;
    invalid_budget EXCEPTION;
    manager_not_found EXCEPTION;
END dept_pkg;
/

-- =====================================================
-- 8C. DEPARTMENT PL/SQL PACKAGE BODY
-- =====================================================

-- Department package body (implementation)
CREATE OR REPLACE PACKAGE BODY dept_pkg AS

    -- Private procedure for logging department activities
    PROCEDURE log_department_activity(
        p_department_id IN NUMBER,
        p_action IN VARCHAR2,
        p_old_budget IN NUMBER DEFAULT NULL,
        p_new_budget IN NUMBER DEFAULT NULL,
        p_old_manager_id IN NUMBER DEFAULT NULL,
        p_new_manager_id IN NUMBER DEFAULT NULL
    ) IS
    BEGIN
        INSERT INTO department_log (log_id, department_id, action, log_date, old_budget, new_budget, old_manager_id, new_manager_id)
        VALUES (dept_log_seq.NEXTVAL, p_department_id, p_action, SYSDATE, p_old_budget, p_new_budget, p_old_manager_id, p_new_manager_id);
        COMMIT;
    END log_department_activity;

    -- Implementation of add_department procedure
    PROCEDURE add_department(
        p_name IN VARCHAR2,
        p_description IN VARCHAR2 DEFAULT NULL,
        p_manager_id IN NUMBER DEFAULT NULL,
        p_budget IN NUMBER DEFAULT NULL,
        p_department_id OUT NUMBER
    ) IS
        v_dept_id NUMBER;
        v_manager_exists NUMBER := 0;
    BEGIN
        -- Validation
        IF p_name IS NULL OR LENGTH(TRIM(p_name)) = 0 THEN
            RAISE_APPLICATION_ERROR(-20101, 'Department name cannot be empty');
        END IF;

        IF p_budget IS NOT NULL AND p_budget < 0 THEN
            RAISE invalid_budget;
        END IF;

        -- Check if manager exists (if provided)
        IF p_manager_id IS NOT NULL THEN
            SELECT COUNT(*) INTO v_manager_exists FROM employees WHERE id = p_manager_id;
            IF v_manager_exists = 0 THEN
                RAISE manager_not_found;
            END IF;
        END IF;

        -- Get next department ID
        SELECT dept_seq.NEXTVAL INTO v_dept_id FROM dual;

        -- Insert department
        INSERT INTO departments (id, name, description, manager_id, budget, created_date)
        VALUES (v_dept_id, TRIM(p_name), p_description, p_manager_id, p_budget, SYSDATE);

        -- Log the activity
        log_department_activity(v_dept_id, 'INSERT', NULL, p_budget, NULL, p_manager_id);

        -- Return the generated ID
        p_department_id := v_dept_id;

        COMMIT;

    EXCEPTION
        WHEN invalid_budget THEN
            RAISE_APPLICATION_ERROR(-20102, 'Budget cannot be negative');
        WHEN manager_not_found THEN
            RAISE_APPLICATION_ERROR(-20103, 'Manager not found with ID: ' || p_manager_id);
        WHEN OTHERS THEN
            ROLLBACK;
            RAISE;
    END add_department;

    -- Implementation of get_department_by_id function
    FUNCTION get_department_by_id(p_department_id IN NUMBER) RETURN VARCHAR2 IS
        v_name VARCHAR2(100);
    BEGIN
        -- Validation
        IF p_department_id IS NULL OR p_department_id <= 0 THEN
            RAISE_APPLICATION_ERROR(-20104, 'Invalid department ID');
        END IF;

        -- Get department name
        SELECT name INTO v_name
        FROM departments
        WHERE id = p_department_id;

        RETURN v_name;

    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RAISE department_not_found;
        WHEN department_not_found THEN
            RAISE_APPLICATION_ERROR(-20105, 'Department not found with ID: ' || p_department_id);
        WHEN OTHERS THEN
            RAISE;
    END get_department_by_id;

    -- Implementation of get_department_count function
    FUNCTION get_department_count RETURN NUMBER IS
        v_count NUMBER;
    BEGIN
        SELECT COUNT(*) INTO v_count FROM departments;
        RETURN v_count;
    END get_department_count;

    -- Implementation of update_department procedure
    PROCEDURE update_department(
        p_department_id IN NUMBER,
        p_name IN VARCHAR2 DEFAULT NULL,
        p_description IN VARCHAR2 DEFAULT NULL,
        p_manager_id IN NUMBER DEFAULT NULL,
        p_budget IN NUMBER DEFAULT NULL
    ) IS
        v_old_budget NUMBER(12,2);
        v_old_manager_id NUMBER;
        v_department_exists NUMBER;
        v_manager_exists NUMBER := 0;
    BEGIN
        -- Check if department exists and get current values
        SELECT COUNT(*), NVL(MAX(budget), 0), NVL(MAX(manager_id), 0)
        INTO v_department_exists, v_old_budget, v_old_manager_id
        FROM departments
        WHERE id = p_department_id;

        IF v_department_exists = 0 THEN
            RAISE department_not_found;
        END IF;

        -- Validate budget if provided
        IF p_budget IS NOT NULL AND p_budget < 0 THEN
            RAISE invalid_budget;
        END IF;

        -- Check if manager exists (if provided)
        IF p_manager_id IS NOT NULL THEN
            SELECT COUNT(*) INTO v_manager_exists FROM employees WHERE id = p_manager_id;
            IF v_manager_exists = 0 THEN
                RAISE manager_not_found;
            END IF;
        END IF;

        -- Update department (only update provided fields)
        UPDATE departments
        SET name = NVL(p_name, name),
            description = NVL(p_description, description),
            manager_id = NVL(p_manager_id, manager_id),
            budget = NVL(p_budget, budget)
        WHERE id = p_department_id;

        -- Log the activity
        log_department_activity(p_department_id, 'UPDATE', v_old_budget, p_budget, v_old_manager_id, p_manager_id);

        COMMIT;

    EXCEPTION
        WHEN department_not_found THEN
            RAISE_APPLICATION_ERROR(-20105, 'Department not found with ID: ' || p_department_id);
        WHEN invalid_budget THEN
            RAISE_APPLICATION_ERROR(-20102, 'Budget cannot be negative');
        WHEN manager_not_found THEN
            RAISE_APPLICATION_ERROR(-20103, 'Manager not found with ID: ' || p_manager_id);
        WHEN OTHERS THEN
            ROLLBACK;
            RAISE;
    END update_department;

    -- Implementation of get_departments_by_manager function
    FUNCTION get_departments_by_manager(p_manager_id IN NUMBER) RETURN NUMBER IS
        v_count NUMBER;
    BEGIN
        SELECT COUNT(*) INTO v_count
        FROM departments
        WHERE manager_id = p_manager_id;

        RETURN v_count;
    END get_departments_by_manager;

    -- Implementation of assign_manager procedure
    PROCEDURE assign_manager(
        p_department_id IN NUMBER,
        p_manager_id IN NUMBER
    ) IS
        v_old_manager_id NUMBER;
        v_department_exists NUMBER;
        v_manager_exists NUMBER := 0;
    BEGIN
        -- Check if department exists and get current manager
        SELECT COUNT(*), NVL(MAX(manager_id), 0)
        INTO v_department_exists, v_old_manager_id
        FROM departments
        WHERE id = p_department_id;

        IF v_department_exists = 0 THEN
            RAISE department_not_found;
        END IF;

        -- Check if manager exists
        SELECT COUNT(*) INTO v_manager_exists FROM employees WHERE id = p_manager_id;
        IF v_manager_exists = 0 THEN
            RAISE manager_not_found;
        END IF;

        -- Update manager
        UPDATE departments
        SET manager_id = p_manager_id
        WHERE id = p_department_id;

        -- Log the activity
        log_department_activity(p_department_id, 'ASSIGN_MANAGER', NULL, NULL, v_old_manager_id, p_manager_id);

        COMMIT;

    EXCEPTION
        WHEN department_not_found THEN
            RAISE_APPLICATION_ERROR(-20105, 'Department not found with ID: ' || p_department_id);
        WHEN manager_not_found THEN
            RAISE_APPLICATION_ERROR(-20103, 'Manager not found with ID: ' || p_manager_id);
        WHEN OTHERS THEN
            ROLLBACK;
            RAISE;
    END assign_manager;

END dept_pkg;
/

-- =====================================================
-- 9. PL/SQL TRIGGERS
-- =====================================================

-- Trigger for auditing employee table changes
CREATE OR REPLACE TRIGGER trg_employee_audit
    BEFORE INSERT OR UPDATE OR DELETE ON employees
    FOR EACH ROW
DECLARE
    v_action VARCHAR2(50);
    v_old_salary NUMBER(10,2);
    v_new_salary NUMBER(10,2);
    v_employee_id NUMBER;
BEGIN
    -- Determine the action
    IF INSERTING THEN
        v_action := 'INSERT';
        v_employee_id := :NEW.id;
        v_old_salary := NULL;
        v_new_salary := :NEW.salary;
    ELSIF UPDATING THEN
        v_action := 'UPDATE';
        v_employee_id := :NEW.id;
        v_old_salary := :OLD.salary;
        v_new_salary := :NEW.salary;

        -- More specific action for salary updates
        IF :OLD.salary != :NEW.salary THEN
            v_action := 'SALARY_UPDATE';
        END IF;
    ELSIF DELETING THEN
        v_action := 'DELETE';
        v_employee_id := :OLD.id;
        v_old_salary := :OLD.salary;
        v_new_salary := NULL;
    END IF;

    -- Insert audit record
    INSERT INTO employee_log (log_id, employee_id, action, log_date, old_salary, new_salary)
    VALUES (log_seq.NEXTVAL, v_employee_id, v_action, SYSDATE, v_old_salary, v_new_salary);

EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't prevent the main operation
        DBMS_OUTPUT.PUT_LINE('Audit trigger error: ' || SQLERRM);
END trg_employee_audit;
/

-- Trigger to validate employee data before insert/update
CREATE OR REPLACE TRIGGER trg_employee_validation
    BEFORE INSERT OR UPDATE ON employees
    FOR EACH ROW
BEGIN
    -- Validate employee name
    IF :NEW.name IS NULL OR LENGTH(TRIM(:NEW.name)) = 0 THEN
        RAISE_APPLICATION_ERROR(-20001, 'Employee name cannot be empty');
    END IF;

    -- Validate salary
    IF :NEW.salary < 0 THEN
        RAISE_APPLICATION_ERROR(-20002, 'Salary cannot be negative');
    END IF;

    IF :NEW.salary > 999999.99 THEN
        RAISE_APPLICATION_ERROR(-20006, 'Salary cannot exceed $999,999.99');
    END IF;

    -- Validate hire date
    IF :NEW.hire_date > SYSDATE THEN
        RAISE_APPLICATION_ERROR(-20003, 'Hire date cannot be in the future');
    END IF;

    -- Trim and format name
    :NEW.name := TRIM(:NEW.name);

    -- Set created_date for new records
    IF INSERTING AND :NEW.created_date IS NULL THEN
        :NEW.created_date := SYSDATE;
    END IF;
END trg_employee_validation;
/

-- Trigger for auditing department table changes
CREATE OR REPLACE TRIGGER trg_department_audit
    BEFORE INSERT OR UPDATE OR DELETE ON departments
    FOR EACH ROW
DECLARE
    v_action VARCHAR2(50);
    v_old_budget NUMBER(12,2);
    v_new_budget NUMBER(12,2);
    v_old_manager_id NUMBER;
    v_new_manager_id NUMBER;
    v_department_id NUMBER;
BEGIN
    -- Determine the action
    IF INSERTING THEN
        v_action := 'INSERT';
        v_department_id := :NEW.id;
        v_old_budget := NULL;
        v_new_budget := :NEW.budget;
        v_old_manager_id := NULL;
        v_new_manager_id := :NEW.manager_id;
    ELSIF UPDATING THEN
        v_action := 'UPDATE';
        v_department_id := :NEW.id;
        v_old_budget := :OLD.budget;
        v_new_budget := :NEW.budget;
        v_old_manager_id := :OLD.manager_id;
        v_new_manager_id := :NEW.manager_id;

        -- More specific action for budget updates
        IF NVL(:OLD.budget, 0) != NVL(:NEW.budget, 0) THEN
            v_action := 'BUDGET_UPDATE';
        END IF;

        -- More specific action for manager updates
        IF NVL(:OLD.manager_id, 0) != NVL(:NEW.manager_id, 0) THEN
            v_action := 'MANAGER_UPDATE';
        END IF;
    ELSIF DELETING THEN
        v_action := 'DELETE';
        v_department_id := :OLD.id;
        v_old_budget := :OLD.budget;
        v_new_budget := NULL;
        v_old_manager_id := :OLD.manager_id;
        v_new_manager_id := NULL;
    END IF;

    -- Insert audit record
    INSERT INTO department_log (log_id, department_id, action, log_date, old_budget, new_budget, old_manager_id, new_manager_id)
    VALUES (dept_log_seq.NEXTVAL, v_department_id, v_action, SYSDATE, v_old_budget, v_new_budget, v_old_manager_id, v_new_manager_id);

EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't prevent the main operation
        DBMS_OUTPUT.PUT_LINE('Department audit trigger error: ' || SQLERRM);
END trg_department_audit;
/

-- Trigger to validate department data before insert/update
CREATE OR REPLACE TRIGGER trg_department_validation
    BEFORE INSERT OR UPDATE ON departments
    FOR EACH ROW
DECLARE
    v_manager_exists NUMBER := 0;
BEGIN
    -- Validate department name
    IF :NEW.name IS NULL OR LENGTH(TRIM(:NEW.name)) = 0 THEN
        RAISE_APPLICATION_ERROR(-20101, 'Department name cannot be empty');
    END IF;

    -- Validate budget
    IF :NEW.budget IS NOT NULL AND :NEW.budget < 0 THEN
        RAISE_APPLICATION_ERROR(-20102, 'Budget cannot be negative');
    END IF;

    IF :NEW.budget IS NOT NULL AND :NEW.budget > 99999999.99 THEN
        RAISE_APPLICATION_ERROR(-20106, 'Budget cannot exceed $99,999,999.99');
    END IF;

    -- Validate manager exists (if provided)
    IF :NEW.manager_id IS NOT NULL THEN
        SELECT COUNT(*) INTO v_manager_exists FROM employees WHERE id = :NEW.manager_id;
        IF v_manager_exists = 0 THEN
            RAISE_APPLICATION_ERROR(-20103, 'Manager not found with ID: ' || :NEW.manager_id);
        END IF;
    END IF;

    -- Trim and format name
    :NEW.name := TRIM(:NEW.name);

    -- Set created_date for new records
    IF INSERTING AND :NEW.created_date IS NULL THEN
        :NEW.created_date := SYSDATE;
    END IF;
END trg_department_validation;
/

-- Add foreign key constraint for department manager (after employees table exists)
ALTER TABLE departments ADD CONSTRAINT fk_dept_manager FOREIGN KEY (manager_id) REFERENCES employees(id);

-- =====================================================
-- 10. DEMONSTRATION PROCEDURES
-- =====================================================

-- Procedure to demonstrate all PL/SQL constructs
CREATE OR REPLACE PROCEDURE demo_plsql_features AS
    -- Variables
    v_employee_id NUMBER;
    v_salary NUMBER(10,2);
    v_bonus NUMBER(10,2);
    v_count NUMBER;

    -- Cursor for high-salary employees
    CURSOR high_salary_cursor IS
        SELECT id, name, salary
        FROM employees
        WHERE salary > 60000
        ORDER BY salary DESC;

    v_emp_record high_salary_cursor%ROWTYPE;
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== PL/SQL FEATURES DEMONSTRATION ===');
    DBMS_OUTPUT.PUT_LINE('Date: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('');

    -- 1. Variables and Package Function
    v_count := emp_pkg.get_employee_count;
    DBMS_OUTPUT.PUT_LINE('1. VARIABLES & FUNCTIONS:');
    DBMS_OUTPUT.PUT_LINE('   Total employees: ' || v_count);

    -- 2. Conditional Logic
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('2. CONDITIONAL LOGIC:');
    IF v_count > 0 THEN
        DBMS_OUTPUT.PUT_LINE('   Employee database is populated');

        -- Get first employee for demonstration
        SELECT id INTO v_employee_id FROM employees WHERE ROWNUM = 1;
        v_salary := emp_pkg.get_salary_by_id(v_employee_id);

        IF v_salary >= 70000 THEN
            DBMS_OUTPUT.PUT_LINE('   First employee has high salary: $' || v_salary);
        ELSIF v_salary >= 50000 THEN
            DBMS_OUTPUT.PUT_LINE('   First employee has medium salary: $' || v_salary);
        ELSE
            DBMS_OUTPUT.PUT_LINE('   First employee has entry-level salary: $' || v_salary);
        END IF;

        -- Calculate bonus
        v_bonus := emp_pkg.calculate_annual_bonus(v_employee_id, 12);
        DBMS_OUTPUT.PUT_LINE('   Annual bonus (12%): $' || TO_CHAR(v_bonus, '999,999.99'));
    ELSE
        DBMS_OUTPUT.PUT_LINE('   No employees in database');
    END IF;

    -- 3. Cursor Usage
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('3. CURSOR DEMONSTRATION:');
    DBMS_OUTPUT.PUT_LINE('   High-salary employees (>$60,000):');

    OPEN high_salary_cursor;
    LOOP
        FETCH high_salary_cursor INTO v_emp_record;
        EXIT WHEN high_salary_cursor%NOTFOUND;

        DBMS_OUTPUT.PUT_LINE('   - ' || v_emp_record.name || ': $' ||
                           TO_CHAR(v_emp_record.salary, '999,999.99'));
    END LOOP;
    CLOSE high_salary_cursor;

    -- 4. Loop Demonstration
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('4. LOOP DEMONSTRATION:');
    DBMS_OUTPUT.PUT_LINE('   Processing all employees:');

    FOR emp_rec IN (SELECT id, name, salary FROM employees ORDER BY id) LOOP
        v_bonus := emp_pkg.calculate_annual_bonus(emp_rec.id, 10);
        DBMS_OUTPUT.PUT_LINE('   Employee ' || emp_rec.id || ': ' || emp_rec.name ||
                           ' - Bonus: $' || TO_CHAR(v_bonus, '999,999.99'));
    END LOOP;

    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== DEMONSTRATION COMPLETE ===');

EXCEPTION
    WHEN NO_DATA_FOUND THEN
        DBMS_OUTPUT.PUT_LINE('No data found for demonstration');
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error in demonstration: ' || SQLERRM);
        IF high_salary_cursor%ISOPEN THEN
            CLOSE high_salary_cursor;
        END IF;
END demo_plsql_features;
/

-- =====================================================
-- 11. INDEXES FOR PERFORMANCE
-- =====================================================

-- Index on employee name for faster searches
CREATE INDEX idx_employees_name ON employees(name);

-- Index on hire_date for date-based queries
CREATE INDEX idx_employees_hire_date ON employees(hire_date);

-- Index on salary for salary-based queries
CREATE INDEX idx_employees_salary ON employees(salary);

-- Composite index on employee_log for audit queries
CREATE INDEX idx_employee_log_emp_date ON employee_log(employee_id, log_date);

-- Department indexes for performance
CREATE INDEX idx_departments_name ON departments(name);
CREATE INDEX idx_departments_manager ON departments(manager_id);
CREATE INDEX idx_departments_budget ON departments(budget);

-- Composite index on department_log for audit queries
CREATE INDEX idx_department_log_dept_date ON department_log(department_id, log_date);

-- Index on employees department_id for joins
CREATE INDEX idx_employees_department ON employees(department_id);

-- =====================================================
-- 12. GRANTS AND PERMISSIONS (Optional)
-- =====================================================

-- Grant permissions to application user (uncomment if needed)
-- GRANT SELECT, INSERT, UPDATE ON employees TO hr_app_user;
-- GRANT SELECT, INSERT, UPDATE ON departments TO hr_app_user;
-- GRANT SELECT ON employee_log TO hr_app_user;
-- GRANT SELECT ON department_log TO hr_app_user;
-- GRANT EXECUTE ON emp_pkg TO hr_app_user;
-- GRANT EXECUTE ON dept_pkg TO hr_app_user;

-- =====================================================
-- 13. COMPLETION MESSAGE
-- =====================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('========================================');
    DBMS_OUTPUT.PUT_LINE('MyHRM Database Schema Created Successfully!');
    DBMS_OUTPUT.PUT_LINE('========================================');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Created Objects:');
    DBMS_OUTPUT.PUT_LINE('- Tables: employees, employee_log, departments, department_log');
    DBMS_OUTPUT.PUT_LINE('- Sequences: emp_seq, log_seq, dept_seq, dept_log_seq');
    DBMS_OUTPUT.PUT_LINE('- Packages: emp_pkg, dept_pkg (with procedures and functions)');
    DBMS_OUTPUT.PUT_LINE('- Triggers: trg_employee_audit, trg_employee_validation, trg_department_audit, trg_department_validation');
    DBMS_OUTPUT.PUT_LINE('- Indexes: Performance optimization indexes for all tables');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('PL/SQL Features Demonstrated:');
    DBMS_OUTPUT.PUT_LINE('✓ Variables and Constants');
    DBMS_OUTPUT.PUT_LINE('✓ Cursors (Explicit)');
    DBMS_OUTPUT.PUT_LINE('✓ Conditional Logic (IF statements)');
    DBMS_OUTPUT.PUT_LINE('✓ Loops (FOR, WHILE, Basic)');
    DBMS_OUTPUT.PUT_LINE('✓ Stored Procedures');
    DBMS_OUTPUT.PUT_LINE('✓ Functions');
    DBMS_OUTPUT.PUT_LINE('✓ Triggers');
    DBMS_OUTPUT.PUT_LINE('✓ Packages');
    DBMS_OUTPUT.PUT_LINE('✓ Exception Handling');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('Ready for C# application integration!');
    DBMS_OUTPUT.PUT_LINE('========================================');
END;
/