-- =====================================================
-- MyHRM Database Test Script
-- Quick verification of database setup
-- =====================================================

SET SERVEROUTPUT ON;
SET PAGESIZE 50;
SET LINESIZE 120;

BEGIN
    DBMS_OUTPUT.PUT_LINE('=== MyHRM Database Test ===');
    DBMS_OUTPUT.PUT_LINE('Date: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('');
END;
/

-- Test 1: Check if tables exist and have data
PROMPT === Test 1: Table Verification ===
SELECT 'employees' as table_name, COUNT(*) as record_count FROM employees
UNION ALL
SELECT 'employee_log' as table_name, COUNT(*) as record_count FROM employee_log
ORDER BY table_name;

-- Test 2: Check if sequences exist
PROMPT === Test 2: Sequence Verification ===
SELECT sequence_name, last_number, increment_by 
FROM user_sequences 
WHERE sequence_name IN ('EMP_SEQ', 'LOG_SEQ')
ORDER BY sequence_name;

-- Test 3: Check if package exists and is valid
PROMPT === Test 3: Package Verification ===
SELECT object_name, object_type, status 
FROM user_objects 
WHERE object_name = 'EMP_PKG'
ORDER BY object_type;

-- Test 4: Check if triggers exist and are enabled
PROMPT === Test 4: Trigger Verification ===
SELECT trigger_name, status, triggering_event, table_name
FROM user_triggers
WHERE table_name = 'EMPLOYEES'
ORDER BY trigger_name;

-- Test 5: Test package functions
PROMPT === Test 5: Package Function Tests ===
DECLARE
    v_count NUMBER;
    v_salary NUMBER(10,2);
    v_bonus NUMBER(10,2);
    v_emp_id NUMBER := 1;
BEGIN
    -- Test get_employee_count function
    v_count := emp_pkg.get_employee_count;
    DBMS_OUTPUT.PUT_LINE('Employee count: ' || v_count);
    
    -- Test get_salary_by_id function (if employees exist)
    IF v_count > 0 THEN
        BEGIN
            v_salary := emp_pkg.get_salary_by_id(v_emp_id);
            DBMS_OUTPUT.PUT_LINE('Employee ' || v_emp_id || ' salary: $' || TO_CHAR(v_salary, '999,999.99'));
            
            -- Test calculate_annual_bonus function
            v_bonus := emp_pkg.calculate_annual_bonus(v_emp_id, 10);
            DBMS_OUTPUT.PUT_LINE('Employee ' || v_emp_id || ' bonus (10%): $' || TO_CHAR(v_bonus, '999,999.99'));
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('Error testing functions: ' || SQLERRM);
        END;
    ELSE
        DBMS_OUTPUT.PUT_LINE('No employees found for function testing');
    END IF;
END;
/

-- Test 6: Sample employee data
PROMPT === Test 6: Sample Employee Data ===
SELECT 
    id,
    name,
    TO_CHAR(hire_date, 'YYYY-MM-DD') as hire_date,
    TO_CHAR(salary, '$999,999.99') as salary
FROM employees 
ORDER BY id;

-- Test 7: Recent audit log entries
PROMPT === Test 7: Recent Audit Log Entries ===
SELECT 
    log_id,
    employee_id,
    action,
    TO_CHAR(log_date, 'YYYY-MM-DD HH24:MI:SS') as log_date,
    TO_CHAR(old_salary, '$999,999.99') as old_salary,
    TO_CHAR(new_salary, '$999,999.99') as new_salary
FROM employee_log 
ORDER BY log_date DESC, log_id DESC
FETCH FIRST 10 ROWS ONLY;

-- Test 8: Test adding a new employee via package
PROMPT === Test 8: Test Adding New Employee ===
DECLARE
    v_employee_id NUMBER;
    v_test_name VARCHAR2(100) := 'Test Employee ' || TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS');
    v_test_salary NUMBER(10,2) := 60000.00;
BEGIN
    -- Add test employee
    emp_pkg.add_employee(
        p_name => v_test_name,
        p_hire_date => SYSDATE,
        p_salary => v_test_salary,
        p_employee_id => v_employee_id
    );
    
    DBMS_OUTPUT.PUT_LINE('✓ Successfully added test employee: ' || v_test_name);
    DBMS_OUTPUT.PUT_LINE('  Employee ID: ' || v_employee_id);
    DBMS_OUTPUT.PUT_LINE('  Salary: $' || TO_CHAR(v_test_salary, '999,999.99'));
    
    -- Verify the employee was added
    DECLARE
        v_retrieved_salary NUMBER(10,2);
    BEGIN
        v_retrieved_salary := emp_pkg.get_salary_by_id(v_employee_id);
        DBMS_OUTPUT.PUT_LINE('✓ Verification: Retrieved salary $' || TO_CHAR(v_retrieved_salary, '999,999.99'));
    END;
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('✗ Error adding test employee: ' || SQLERRM);
END;
/

-- Final summary
PROMPT === Test Summary ===
DECLARE
    v_emp_count NUMBER;
    v_log_count NUMBER;
    v_pkg_count NUMBER;
    v_trigger_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_emp_count FROM employees;
    SELECT COUNT(*) INTO v_log_count FROM employee_log;
    SELECT COUNT(*) INTO v_pkg_count FROM user_objects WHERE object_name = 'EMP_PKG' AND status = 'VALID';
    SELECT COUNT(*) INTO v_trigger_count FROM user_triggers WHERE table_name = 'EMPLOYEES' AND status = 'ENABLED';
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== DATABASE TEST RESULTS ===');
    DBMS_OUTPUT.PUT_LINE('Employees: ' || v_emp_count || ' records');
    DBMS_OUTPUT.PUT_LINE('Audit logs: ' || v_log_count || ' records');
    DBMS_OUTPUT.PUT_LINE('Package status: ' || CASE WHEN v_pkg_count > 0 THEN 'VALID' ELSE 'INVALID' END);
    DBMS_OUTPUT.PUT_LINE('Triggers: ' || v_trigger_count || ' enabled');
    
    IF v_emp_count > 0 AND v_log_count > 0 AND v_pkg_count > 0 AND v_trigger_count > 0 THEN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('✓ ALL TESTS PASSED - Database is ready!');
    ELSE
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('✗ SOME TESTS FAILED - Check setup');
    END IF;
    DBMS_OUTPUT.PUT_LINE('=============================');
END;
/
