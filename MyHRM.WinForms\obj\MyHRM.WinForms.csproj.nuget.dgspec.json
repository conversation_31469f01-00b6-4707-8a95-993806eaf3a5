{"format": 1, "restore": {"E:\\project\\MyHRM\\MyHRM.WinForms\\MyHRM.WinForms.csproj": {}}, "projects": {"E:\\project\\MyHRM\\MyHRM.WinForms\\MyHRM.WinForms.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\project\\MyHRM\\MyHRM.WinForms\\MyHRM.WinForms.csproj", "projectName": "MyHRM.WinForms", "projectPath": "E:\\project\\MyHRM\\MyHRM.WinForms\\MyHRM.WinForms.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\project\\MyHRM\\MyHRM.WinForms\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[3.21.120, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.414\\RuntimeIdentifierGraph.json"}}}}}