using MyHRM.WinForms.BusinessLogic;
using MyHRM.WinForms.Models;

namespace MyHRM.WinForms
{
    /// <summary>
    /// Main form for the HR Management System
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly EmployeeService _employeeService;
        private readonly DepartmentService _departmentService;

        public MainForm()
        {
            InitializeComponent();
            _employeeService = new EmployeeService();
            _departmentService = new DepartmentService();
        }

        private async void MainForm_Load(object sender, EventArgs e)
        {
            UpdateStatus("Application started. Ready to connect to Oracle database.");

            // Set default values
            dtpHireDate.Value = DateTime.Now;
            txtSalary.Text = "50000.00";
            txtDeptBudget.Text = "100000.00";

            // Load manager combo box
            await LoadManagerComboBox();

            // Test connection on startup
            await TestDatabaseConnection();
        }

        private async void btnAddEmployee_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("Please enter an employee name.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                if (!decimal.TryParse(txtSalary.Text, out decimal salary))
                {
                    MessageBox.Show("Please enter a valid salary amount.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtSalary.Focus();
                    return;
                }

                // Additional validation using business logic
                var validation = _employeeService.ValidateEmployee(txtName.Text, dtpHireDate.Value, salary);
                if (!validation.IsValid)
                {
                    MessageBox.Show(validation.ErrorMessage, "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                UpdateStatus("Adding employee...");
                btnAddEmployee.Enabled = false;

                // Add employee using stored procedure
                int employeeId = await _employeeService.AddEmployeeAsync(txtName.Text, dtpHireDate.Value, salary);

                MessageBox.Show($"Employee added successfully with ID: {employeeId}", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Clear form
                ClearEmployeeForm();

                // Refresh employee list
                await LoadEmployees();

                UpdateStatus($"Employee added successfully. ID: {employeeId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding employee: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus($"Error: {ex.Message}");
            }
            finally
            {
                btnAddEmployee.Enabled = true;
            }
        }

        private async void btnViewEmployees_Click(object sender, EventArgs e)
        {
            await LoadEmployees();
        }

        private async void btnTestConnection_Click(object sender, EventArgs e)
        {
            await TestDatabaseConnection();
        }

        private async void btnTestPLSQL_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("Testing PL/SQL functionality...");

                // Test 1: Add a sample employee using stored procedure
                string testName = $"Test Employee {DateTime.Now:HHmmss}";
                DateTime testHireDate = DateTime.Now.AddDays(-30);
                decimal testSalary = 75000.00m;

                int employeeId = await _employeeService.AddEmployeeAsync(testName, testHireDate, testSalary);

                // Test 2: Get salary using function
                decimal retrievedSalary = await _employeeService.GetEmployeeSalaryAsync(employeeId);

                // Test 3: Show audit logs (trigger functionality)
                await LoadLogs();

                string message = $"PL/SQL Test Results:\n\n" +
                               $"✓ Stored Procedure: Employee added with ID {employeeId}\n" +
                               $"✓ Function: Retrieved salary ${retrievedSalary:N2}\n" +
                               $"✓ Trigger: Audit log created (check logs below)\n\n" +
                               $"All PL/SQL components are working correctly!";

                MessageBox.Show(message, "PL/SQL Test Results",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Refresh displays
                await LoadEmployees();

                UpdateStatus("PL/SQL test completed successfully.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"PL/SQL test failed: {ex.Message}", "Test Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus($"PL/SQL test failed: {ex.Message}");
            }
        }

        private async void btnViewLogs_Click(object sender, EventArgs e)
        {
            await LoadLogs();
        }

        private async Task TestDatabaseConnection()
        {
            try
            {
                UpdateStatus("Testing database connection...");
                bool isConnected = await _employeeService.TestDatabaseConnectionAsync();

                if (isConnected)
                {
                    UpdateStatus("✓ Database connection successful.");
                    MessageBox.Show("Database connection successful!", "Connection Test",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    UpdateStatus("✗ Database connection failed.");
                    MessageBox.Show("Database connection failed!", "Connection Test",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"✗ Connection error: {ex.Message}");
                MessageBox.Show($"Connection error: {ex.Message}", "Connection Test",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadEmployees()
        {
            try
            {
                UpdateStatus("Loading employees...");
                var employees = await _employeeService.GetAllEmployeesAsync();

                dgvEmployees.DataSource = employees;

                // Format columns
                if (dgvEmployees.Columns["Salary"] != null)
                {
                    dgvEmployees.Columns["Salary"].DefaultCellStyle.Format = "C2";
                }

                if (dgvEmployees.Columns["HireDate"] != null)
                {
                    dgvEmployees.Columns["HireDate"].DefaultCellStyle.Format = "yyyy-MM-dd";
                }

                UpdateStatus($"Loaded {employees.Count} employees.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading employees: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus($"Error loading employees: {ex.Message}");
            }
        }

        private async Task LoadLogs()
        {
            try
            {
                UpdateStatus("Loading audit logs...");
                var logs = await _employeeService.GetEmployeeLogsAsync();

                dgvLogs.DataSource = logs;

                // Format log date column
                if (dgvLogs.Columns["LogDate"] != null)
                {
                    dgvLogs.Columns["LogDate"].DefaultCellStyle.Format = "yyyy-MM-dd HH:mm:ss";
                }

                UpdateStatus($"Loaded {logs.Count} log entries.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading logs: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus($"Error loading logs: {ex.Message}");
            }
        }

        private void ClearEmployeeForm()
        {
            txtName.Clear();
            dtpHireDate.Value = DateTime.Now;
            txtSalary.Text = "50000.00";
            txtName.Focus();
        }

        // =====================================================
        // DEPARTMENT METHODS
        // =====================================================

        private async void btnAddDepartment_Click(object sender, EventArgs e)
        {
            try
            {
                // Basic validation
                if (string.IsNullOrWhiteSpace(txtDeptName.Text))
                {
                    MessageBox.Show("Please enter a department name.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtDeptName.Focus();
                    return;
                }

                // Parse budget if provided
                decimal? budget = null;
                if (!string.IsNullOrWhiteSpace(txtDeptBudget.Text))
                {
                    if (!decimal.TryParse(txtDeptBudget.Text, out decimal budgetValue))
                    {
                        MessageBox.Show("Please enter a valid budget amount.", "Validation Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtDeptBudget.Focus();
                        return;
                    }
                    budget = budgetValue;
                }

                // Get selected manager ID
                int? managerId = null;
                if (cmbDeptManager.SelectedValue != null && cmbDeptManager.SelectedValue != DBNull.Value)
                {
                    managerId = (int)cmbDeptManager.SelectedValue;
                }

                // Additional validation using business logic
                var validation = _departmentService.ValidateDepartment(txtDeptName.Text, txtDeptDescription.Text, managerId, budget);
                if (!validation.IsValid)
                {
                    MessageBox.Show(validation.ErrorMessage, "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                UpdateStatus("Adding department...");
                btnAddDepartment.Enabled = false;

                // Add department using stored procedure
                int departmentId = await _departmentService.AddDepartmentAsync(txtDeptName.Text, txtDeptDescription.Text, managerId, budget);

                MessageBox.Show($"Department added successfully with ID: {departmentId}", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Clear form
                ClearDepartmentForm();

                // Refresh department list
                await LoadDepartments();

                UpdateStatus($"Department added successfully. ID: {departmentId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding department: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus($"Error: {ex.Message}");
            }
            finally
            {
                btnAddDepartment.Enabled = true;
            }
        }

        private async void btnViewDepartments_Click(object sender, EventArgs e)
        {
            await LoadDepartments();
        }

        private async void btnViewDeptLogs_Click(object sender, EventArgs e)
        {
            await LoadDepartmentLogs();
        }

        private async Task LoadDepartments()
        {
            try
            {
                UpdateStatus("Loading departments...");
                var departments = await _departmentService.GetAllDepartmentsAsync();

                dgvDepartments.DataSource = departments;

                // Format columns
                if (dgvDepartments.Columns["Budget"] != null)
                {
                    dgvDepartments.Columns["Budget"].DefaultCellStyle.Format = "C2";
                }

                if (dgvDepartments.Columns["CreatedDate"] != null)
                {
                    dgvDepartments.Columns["CreatedDate"].DefaultCellStyle.Format = "yyyy-MM-dd";
                }

                UpdateStatus($"Loaded {departments.Count} departments.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading departments: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus($"Error loading departments: {ex.Message}");
            }
        }

        private async Task LoadDepartmentLogs()
        {
            try
            {
                UpdateStatus("Loading department audit logs...");
                var logs = await _departmentService.GetDepartmentLogsAsync();

                dgvLogs.DataSource = logs;

                // Format log date column
                if (dgvLogs.Columns["LogDate"] != null)
                {
                    dgvLogs.Columns["LogDate"].DefaultCellStyle.Format = "yyyy-MM-dd HH:mm:ss";
                }

                UpdateStatus($"Loaded {logs.Count} department log entries.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading department logs: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus($"Error loading department logs: {ex.Message}");
            }
        }

        private async Task LoadManagerComboBox()
        {
            try
            {
                var employees = await _departmentService.GetEmployeesForSelectionAsync();

                // Add empty option
                var employeeList = new List<Employee> { new Employee { Id = 0, Name = "-- No Manager --" } };
                employeeList.AddRange(employees);

                cmbDeptManager.DataSource = employeeList;
                cmbDeptManager.DisplayMember = "Name";
                cmbDeptManager.ValueMember = "Id";
                cmbDeptManager.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error loading managers: {ex.Message}");
            }
        }

        private void ClearDepartmentForm()
        {
            txtDeptName.Clear();
            txtDeptDescription.Clear();
            txtDeptBudget.Clear();
            cmbDeptManager.SelectedIndex = 0;
            txtDeptName.Focus();
        }

        private void UpdateStatus(string message)
        {
            lblStatus.Text = $"{DateTime.Now:HH:mm:ss} - {message}";
            Application.DoEvents();
        }
    }
}
